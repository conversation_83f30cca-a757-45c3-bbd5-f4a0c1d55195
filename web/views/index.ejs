<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link rel="stylesheet" href="/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/flag-icon-css/6.6.6/css/flag-icons.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-robot"></i> KI Chat Bot</h1>
            <p class="subtitle">Zwei KI-Modelle Gesprächsmonitor</p>
        </header>

        <div class="control-panel-wrapper">
            <!-- Box 1: Ollama Status + Controls -->
            <div class="ollama-control-box">
                <div class="box-header">
                    <h3><i class="fas fa-server"></i> Ollama Control</h3>
                </div>
                <div class="status-row">
                    <div class="status-item">
                        <span class="label">Ollama:</span>
                        <span id="ollama-status" class="status-indicator">Prüfe...</span>
                    </div>
                    <div class="status-item">
                        <span class="label">Chat:</span>
                        <span id="chat-status" class="status-indicator">Gestoppt</span>
                    </div>
                    <div class="status-item">
                        <span class="label">Nachrichten:</span>
                        <span id="message-count" class="status-value">0</span>
                    </div>
                    <div class="status-item">
                        <span class="label">Verbindung:</span>
                        <span id="connection-status" class="status-indicator">Verbinde...</span>
                    </div>
                </div>
                <div class="control-buttons">
                    <button id="start-btn" class="btn btn-primary">
                        <i class="fas fa-play"></i> <span class="btn-text">Start</span>
                    </button>
                    <button id="stop-btn" class="btn btn-danger" disabled>
                        <i class="fas fa-stop"></i> <span class="btn-text">Stop</span>
                    </button>
                    <button id="restart-btn" class="btn btn-warning">
                        <i class="fas fa-redo"></i> <span class="btn-text">Restart</span>
                    </button>
                    <button id="clear-btn" class="btn btn-secondary">
                        <i class="fas fa-trash"></i> <span class="btn-text">Clear</span>
                    </button>
                </div>
            </div>

            <!-- Box 2: Personal Setup (Alice + Bob + Config) -->
            <div class="personal-setup-box">
                <div class="box-header">
                    <h3><i class="fas fa-users"></i> Personal Setup</h3>
                </div>
                <div class="agents-row">
                    <div class="agent-card" id="agent1-card">
                        <div class="agent-header">
                            <span class="agent-icon">🔵</span>
                            <span class="agent-name">Alice</span>
                        </div>
                        <div class="agent-info">
                            <span class="agent-model">Modell: Lädt...</span>
                            <span class="agent-capabilities">Standard</span>
                            <span class="agent-status">Bereit</span>
                        </div>
                    </div>
                    <div class="agent-card" id="agent2-card">
                        <div class="agent-header">
                            <span class="agent-icon">🔴</span>
                            <span class="agent-name">Bob</span>
                        </div>
                        <div class="agent-info">
                            <span class="agent-model">Modell: Lädt...</span>
                            <span class="agent-capabilities">Standard</span>
                            <span class="agent-status">Bereit</span>
                        </div>
                    </div>
                </div>
                <div class="config-section">
                    <div class="model-selectors">
                        <div class="model-selector">
                            <label for="agent1-model">Alice:</label>
                            <select id="agent1-model" class="model-dropdown">
                                <option value="">Modell...</option>
                            </select>
                        </div>
                        <div class="model-selector">
                            <label for="agent2-model">Bob:</label>
                            <select id="agent2-model" class="model-dropdown">
                                <option value="">Modell...</option>
                            </select>
                        </div>
                    </div>
                    <div class="topic-config">
                        <div class="language-selector">
                            <label><span class="label-text">Sprache:</span></label>
                            <div class="flag-selector">
                                <input type="radio" id="lang-de" name="language" value="de" checked>
                                <label for="lang-de" class="flag-option" title="Deutsch">
                                    <span class="fi fi-de"></span>
                                </label>
                                <input type="radio" id="lang-en" name="language" value="en">
                                <label for="lang-en" class="flag-option" title="English">
                                    <span class="fi fi-us"></span>
                                </label>
                            </div>
                        </div>
                        <div class="topic-selector">
                            <label for="chat-topic"><span class="label-text">Thema:</span></label>
                            <select id="chat-topic">
                                <option value="">Zufälliges Thema</option>
                                <option value="ai">Künstliche Intelligenz</option>
                                <option value="travel">Reisen</option>
                                <option value="learning">Lernen und Bildung</option>
                                <option value="space">Weltraum und andere Planeten</option>
                                <option value="climate">Klimawandel</option>
                                <option value="friendship">Freundschaft</option>
                                <option value="superpower">Superkräfte</option>
                                <option value="creativity">Kreativität</option>
                                <option value="communication">Kommunikation</option>
                                <option value="custom">Eigenes Thema eingeben...</option>
                            </select>
                        </div>
                        <div id="custom-topic-container" class="custom-topic-container" style="display: none;">
                            <input type="text" id="custom-topic" class="custom-topic-input" placeholder="Gib ein eigenes Gesprächsthema ein...">
                        </div>
                        <button id="apply-config-btn" class="btn btn-primary">
                            <i class="fas fa-check"></i> <span class="btn-text">Übernehmen</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="chat-container">
            <div class="chat-header">
                <h3><i class="fas fa-comments"></i> <span class="header-text">Live Unterhaltung</span></h3>
                <div class="chat-controls">
                    <button id="auto-scroll-btn" class="btn btn-small btn-outline active">
                        <i class="fas fa-arrow-down"></i> <span class="btn-text">Auto</span>
                    </button>
                    <button id="export-btn" class="btn btn-small btn-outline">
                        <i class="fas fa-download"></i> <span class="btn-text">Export</span>
                    </button>
                </div>
            </div>
            <div id="chat-messages" class="chat-messages">
                <div class="welcome-message">
                    <i class="fas fa-info-circle"></i>
                    <p id="welcome-text">Willkommen beim KI Chat Bot! Klicken Sie auf "Start", um eine Unterhaltung zwischen zwei KI-Agenten zu beginnen.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p id="loading-text">Verarbeite...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/socket.io/socket.io.js"></script>
    <script src="/script.js"></script>
    <script>
        // Pass server data to client
        window.APP_CONFIG = {
            ollamaHost: '<%= ollamaHost %>'
        };
    </script>
</body>
</html>