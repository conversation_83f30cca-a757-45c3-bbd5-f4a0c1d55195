const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const fs = require('fs');
const { spawn } = require('child_process');
const axios = require('axios');
require('dotenv').config();
const session = require('express-session');
const bcrypt = require('bcrypt');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"],
    credentials: true
  }
});

const PORT = process.env.WEB_PORT || 3005;
const OLLAMA_HOST = process.env.OLLAMA_HOST || 'http://*********:11223';

// Global variables
let chatProcess = null;
let chatStatus = {
  isRunning: false,
  conversationLength: 0,
  agents: {},
  lastMessage: null
};

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Konfiguration der Session
app.use(session({
    secret: process.env.SESSION_SECRET || 'default-secret-key-change-in-production',
    resave: false,
    saveUninitialized: false
}));

// Middleware für Authentifizierung
const requireAuth = (req, res, next) => {
    if (req.session.authenticated) {
        next();
    } else {
        res.redirect('/login');
    }
};

// Login-Route
app.get('/login', (req, res) => {
    res.render('login');
});

app.post('/login', (req, res) => {
    const { username, password } = req.body;
    console.log('Login attempt:', { username });
    console.log('ADMIN_USER:', process.env.ADMIN_USER);
    console.log('ADMIN_PASS_HASH exists:', !!process.env.ADMIN_PASS_HASH);
    
    // In Produktion: Gegen Datenbank prüfen
    if (username === process.env.ADMIN_USER && 
        bcrypt.compareSync(password, process.env.ADMIN_PASS_HASH)) {
        console.log('Login successful');
        req.session.authenticated = true;
        res.redirect('/');
    } else {
        console.log('Login failed');
        res.redirect('/login');
    }
});

// Routes
app.get('/', requireAuth, (req, res) => {
  res.render('index', { 
    title: 'AI Chat Bot - Two LLMs Conversation',
    ollamaHost: OLLAMA_HOST
  });
});

// API Routes
app.get('/api/status', (req, res) => {
  res.json(chatStatus);
});

app.get('/api/conversation', (req, res) => {
  const logDir = path.join(__dirname, '..', 'logs');
  const today = new Date().toISOString().split('T')[0].replace(/-/g, '');
  const logFile = path.join(logDir, `chat_${today}.json`);
  
  try {
    if (fs.existsSync(logFile)) {
      const data = fs.readFileSync(logFile, 'utf8');
      const conversation = JSON.parse(data);
      res.json(conversation);
    } else {
      res.json([]);
    }
  } catch (error) {
    console.error('Error reading conversation log:', error);
    res.status(500).json({ error: 'Failed to read conversation log' });
  }
});

app.post('/api/start-chat', async (req, res) => {
  if (chatProcess) {
    return res.status(400).json({ error: 'Chat is already running' });
  }
  
  try {
    // Test Ollama connection first with retry logic
    let connectionSuccess = false;
    let connectionError = null;
    let retryCount = 0;
    
    while (!connectionSuccess && retryCount < OLLAMA_MAX_RETRIES) {
      try {
        console.log(`Ollama connection attempt ${retryCount + 1}/${OLLAMA_MAX_RETRIES}`);
        await axios.get(`${OLLAMA_HOST}/api/tags`, { timeout: OLLAMA_TEST_TIMEOUT });
        connectionSuccess = true;
        console.log('Ollama connection successful');
      } catch (error) {
        connectionError = error;
        retryCount++;
        console.error(`Ollama connection attempt ${retryCount} failed: ${error.message}`);
        
        if (retryCount < OLLAMA_MAX_RETRIES) {
          console.log(`Waiting 2 seconds before retry...`);
          await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second delay between retries
        }
      }
    }
    
    if (!connectionSuccess) {
      throw new Error(`Failed to connect to Ollama after ${OLLAMA_MAX_RETRIES} attempts: ${connectionError.message}`);
    }
    
    // Clear existing log file before starting new chat
    const logDir = path.join(__dirname, '..', 'logs');
    const today = new Date().toISOString().split('T')[0].replace(/-/g, '');
    const logFile = path.join(logDir, `chat_${today}.json`);
    
    // Delete existing log file to start fresh
    if (fs.existsSync(logFile)) {
      fs.unlinkSync(logFile);
      console.log('Cleared existing chat log for fresh start');
    }
    
    // Debug: Zeige aktuelle Umgebungsvariablen
    console.log('Current environment variables:');
    console.log(`- CUSTOM_TOPIC: ${process.env.CUSTOM_TOPIC}`);
    console.log(`- AGENT1_MODEL: ${process.env.AGENT1_MODEL}`);
    console.log(`- AGENT2_MODEL: ${process.env.AGENT2_MODEL}`);
    console.log(`- CHAT_LANGUAGE: ${process.env.CHAT_LANGUAGE}`);
    
    // Reset chat status
    chatStatus.conversationLength = 0;
    chatStatus.lastMessage = null;
    
    // Prüfen, ob ein benutzerdefiniertes Thema übergeben wurde
    const customTopic = req.body.customTopic || '';
    
    // Wenn ein benutzerdefiniertes Thema angegeben wurde, aktualisiere die Umgebungsvariable
    if (customTopic) {
      process.env.CUSTOM_TOPIC = customTopic;
      console.log(`Updated environment variable CUSTOM_TOPIC to: "${customTopic}"`);
    }
    
    // Start Python chat bot with optional custom topic
    const chatBotArgs = ['chat_bot.py'];
    if (customTopic) {
      // Sicherstellen, dass das Topic korrekt als ein Argument übergeben wird
      chatBotArgs.push('--topic');
      chatBotArgs.push(customTopic);
      console.log(`Starting chat with custom topic: "${customTopic}"`);
    }
    
    console.log('Launching Python bot with args:', chatBotArgs);
    
    // Bereite die Umgebung für den Prozess vor, mit expliziten Umgebungsvariablen
    const env = { ...process.env };
    if (customTopic) {
      env.CUSTOM_TOPIC = customTopic;
    }
    
    // Füge die Timeout-Einstellungen als Umgebungsvariablen hinzu
    env.OLLAMA_TEST_TIMEOUT = process.env.OLLAMA_TEST_TIMEOUT || '30';
    env.OLLAMA_REQUEST_TIMEOUT = process.env.OLLAMA_REQUEST_TIMEOUT || '60';
    env.OLLAMA_MAX_RETRIES = process.env.OLLAMA_MAX_RETRIES || '3';
    
    chatProcess = spawn('python3', chatBotArgs, {
      cwd: path.join(__dirname, '..', 'python'),
      stdio: ['pipe', 'pipe', 'pipe'],
      env: env
    });
    
    chatStatus.isRunning = true;
    
    // Remove any existing listeners before adding new ones
    chatProcess.removeAllListeners();
    
    // Handle process output
    let stdoutBuffer = '';
    
    chatProcess.stdout.on('data', (data) => {
      const output = data.toString();
      
      // Daten zum Buffer hinzufügen
      stdoutBuffer += output;
      
      // Versuche, einen vollständigen AGENT_INFO-Block zu finden
      if (stdoutBuffer.includes('AGENT_INFO:')) {
        try {
          console.log('Found AGENT_INFO in buffer, parsing...');
          
          // Suche nach einer Zeile, die mit AGENT_INFO: beginnt
          const lines = stdoutBuffer.split('\n');
          const agentInfoLine = lines.find(line => line.trim().startsWith('AGENT_INFO:'));
          
          if (agentInfoLine) {
            console.log('Agent info line:', agentInfoLine);
            
            const agentInfoJson = agentInfoLine.replace('AGENT_INFO:', '').trim();
            console.log('Agent info JSON:', agentInfoJson);
            
            try {
              const agentInfo = JSON.parse(agentInfoJson);
              console.log('Parsed agent info:', agentInfo);
              
              // Update chat status with agent info
              chatStatus.agents = agentInfo;
              
              // Broadcast to all clients
              io.emit('agents-update', agentInfo);
              console.log('Agent info sent to clients:', agentInfo);
              
              // Lösche den verarbeiteten Teil aus dem Buffer
              stdoutBuffer = stdoutBuffer.replace(agentInfoLine, '');
            } catch (jsonError) {
              console.error('JSON parsing error:', jsonError);
              console.error('Invalid JSON:', agentInfoJson);
            }
          }
        } catch (error) {
          console.error('Error parsing agent info from buffer:', error);
        }
      }
      
      // Sende die Daten an die Clients
      console.log('Chat Bot Output:', output);
      io.emit('chat-output', { type: 'stdout', data: output });
    });
    
    chatProcess.stderr.on('data', (data) => {
      const output = data.toString();
      console.error('Chat Bot Error:', output);
      io.emit('chat-output', { type: 'stderr', data: output });
    });
    
    chatProcess.on('close', (code) => {
      console.log(`Chat bot process exited with code ${code}`);
      chatStatus.isRunning = false;
      chatProcess = null;
      io.emit('chat-status', chatStatus);
    });
    
    // Monitor conversation log for updates
    startLogMonitoring();
    
    // Notify all clients about fresh start
    io.emit('conversation-update', []);
    io.emit('chat-status', chatStatus);
    res.json({ message: 'Chat started successfully' });
    
  } catch (error) {
    console.error('Error starting chat:', error);
    chatStatus.isRunning = false;
    res.status(500).json({ error: 'Failed to start chat: ' + error.message });
  }
});

app.post('/api/stop-chat', (req, res) => {
  if (!chatProcess) {
    return res.status(400).json({ error: 'No chat is currently running' });
  }
  
  chatProcess.kill('SIGTERM');
  chatStatus.isRunning = false;
  chatProcess = null;
  
  io.emit('chat-status', chatStatus);
  res.json({ message: 'Chat stopped successfully' });
});

app.post('/api/restart-chat', async (req, res) => {
  // Stop current chat if running
  if (chatProcess) {
    chatProcess.kill('SIGTERM');
    chatProcess = null;
  }
  
  // Wait a moment for cleanup
  setTimeout(async () => {
    try {
      // Start new chat
      await axios.post(`http://localhost:${PORT}/api/start-chat`);
      res.json({ message: 'Chat restarted successfully' });
    } catch (error) {
      res.status(500).json({ error: 'Failed to restart chat: ' + error.message });
    }
  }, 1000);
});

app.get('/api/ollama/models', async (req, res) => {
  try {
    const response = await axios.get(`${OLLAMA_HOST}/api/tags`);
    res.json(response.data);
  } catch (error) {
    console.error('Error fetching Ollama models:', error);
    res.status(500).json({ error: 'Failed to fetch models from Ollama' });
  }
});

// Neuer Endpunkt für laufende Modelle (ähnlich zu 'ollama ps')
app.get('/api/ollama/ps', async (req, res) => {
  const appDebugMode = process.env.APP_DEBUG_MODE === 'true';
  try {
    if (appDebugMode) {
      console.log('[WebUI] Fetching running Ollama models (debug)');
    }
    
    // Pfad zum Python-Skript
    const scriptPath = path.join(__dirname, '..', 'python', 'get_running_models.py');
    
    // Führe das Python-Skript aus
    const pythonProcess = spawn('python3', [scriptPath], {
      cwd: path.join(__dirname, '..', 'python')
    });
    
    let output = '';
    let errorOutput = '';
    
    pythonProcess.stdout.on('data', (data) => {
      output += data.toString();
    });
    
    pythonProcess.stderr.on('data', (data) => {
      errorOutput += data.toString();
      console.error('Python stderr:', data.toString());
    });
    
    pythonProcess.on('close', (code) => {
      if (code === 0) {
        try {
          const result = JSON.parse(output.trim());
          // Kompaktere Ausgabe der laufenden Modelle
          if (result.models && result.models.length > 0) {
            const modelNames = result.models.map(m => m.name).join(', ');
            if (appDebugMode) {
              console.log(`[WebUI] Running models: ${modelNames} (debug)`);
            }
          } else {
            if (appDebugMode) {
              console.log('[WebUI] No running models found (debug)');
            }
          }
          res.json(result);
        } catch (parseError) {
          console.error('Error parsing Python output:', parseError);
          res.status(500).json({ error: 'Failed to parse running models data' });
        }
      } else {
        console.error('Python process error:', errorOutput);
        res.status(500).json({ error: 'Failed to get running models from Python' });
      }
    });
  } catch (error) {
    console.error('Error fetching running Ollama models:', error);
    res.status(500).json({ 
      error: 'Failed to fetch running models from Ollama',
      message: error.message
    });
  }
});

// Get timeout from environment variable with default of 30 seconds if not set
const OLLAMA_TEST_TIMEOUT = parseInt(process.env.OLLAMA_TEST_TIMEOUT || '30') * 1000; // Convert to milliseconds
const OLLAMA_REQUEST_TIMEOUT = parseInt(process.env.OLLAMA_REQUEST_TIMEOUT || '60') * 1000; // Default 60 seconds
const OLLAMA_MAX_RETRIES = parseInt(process.env.OLLAMA_MAX_RETRIES || '3'); // Default 3 retries

app.get('/api/ollama/test', async (req, res) => {
  try {
    console.log('Testing Ollama connection to:', OLLAMA_HOST);
    console.log(`Using timeout settings: ${OLLAMA_TEST_TIMEOUT/1000}s timeout, ${OLLAMA_MAX_RETRIES} max retries`);
    
    const response = await axios.get(`${OLLAMA_HOST}/api/tags`, { 
      timeout: OLLAMA_TEST_TIMEOUT,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    });
    console.log('Ollama response status:', response.status);
    console.log('Ollama response data:', response.data);
    
    res.json({ 
      status: 'connected', 
      models: response.data.models?.length || 0,
      host: OLLAMA_HOST,
      modelList: response.data.models?.map(m => m.name) || []
    });
  } catch (error) {
    console.error('Error testing Ollama connection:', {
      message: error.message,
      code: error.code,
      response: error.response?.data
    });
    res.status(500).json({ 
      status: 'error', 
      error: error.message,
      host: OLLAMA_HOST,
      details: error.code || 'Unknown error'
    });
  }
});

// Socket.IO connection handling
// In der Socket.IO connection handling Sektion (um Zeile 220)
io.on('connection', (socket) => {
  console.log('Client connected:', socket.id);
  
  // Send current status to new client
  socket.emit('chat-status', chatStatus);
  
  // Send agent information if available
  if (chatStatus.agents && Object.keys(chatStatus.agents).length > 0) {
    console.log('Sending existing agent info to new client:', chatStatus.agents);
    socket.emit('agents-update', chatStatus.agents);
  }
  
  // Send current conversation - only if chat is running
  if (chatStatus.isRunning) {
    const logDir = path.join(__dirname, '..', 'logs');
    const today = new Date().toISOString().split('T')[0].replace(/-/g, '');
    const logFile = path.join(logDir, `chat_${today}.json`);
    
    try {
      if (fs.existsSync(logFile)) {
        const data = fs.readFileSync(logFile, 'utf8');
        const conversation = JSON.parse(data);
        // Only send if there are actual messages and chat is running
        if (conversation.length > 0) {
          socket.emit('conversation-history', conversation);
        }
      }
    } catch (error) {
      console.error('Error sending conversation history:', error);
    }
  }
  
  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);
  });
});

// Verbesserte clear-logs Route
app.post('/api/clear-logs', (req, res) => {
  const logDir = path.join(__dirname, '..', 'logs');
  const today = new Date().toISOString().split('T')[0].replace(/-/g, '');
  const logFile = path.join(logDir, `chat_${today}.json`);
  
  try {
    // Datei komplett löschen statt leeres Array zu schreiben
    if (fs.existsSync(logFile)) {
      fs.unlinkSync(logFile);
    }
    
    // Chat Status zurücksetzen
    chatStatus.conversationLength = 0;
    chatStatus.lastMessage = null;
    
    // Alle Clients benachrichtigen
    io.emit('conversation-update', []);
    io.emit('chat-status', chatStatus);
    
    res.json({ message: 'Chat-Verlauf erfolgreich gelöscht' });
  } catch (error) {
    console.error('Fehler beim Löschen der Logs:', error);
    res.status(500).json({ error: 'Fehler beim Löschen der Logs' });
  }
});

// Global variables for cleanup
let logMonitoringInterval = null;
let isShuttingDown = false;

// Function to monitor conversation log file
function startLogMonitoring() {
  // Clear existing interval if any
  if (logMonitoringInterval) {
    clearInterval(logMonitoringInterval);
  }
  
  const logDir = path.join(__dirname, '..', 'logs');
  const today = new Date().toISOString().split('T')[0].replace(/-/g, '');
  const logFile = path.join(logDir, `chat_${today}.json`);
  
  let lastSize = 0;
  
  const checkForUpdates = () => {
    try {
      if (fs.existsSync(logFile)) {
        const stats = fs.statSync(logFile);
        if (stats.size > lastSize) {
          lastSize = stats.size;
          const data = fs.readFileSync(logFile, 'utf8');
          const conversation = JSON.parse(data);
          
          chatStatus.conversationLength = conversation.length;
          if (conversation.length > 0) {
            chatStatus.lastMessage = conversation[conversation.length - 1];
          }
          
          io.emit('conversation-update', conversation);
          io.emit('chat-status', chatStatus);
        }
      }
    } catch (error) {
      console.error('Error monitoring log file:', error);
    }
  };
  
  // Check for updates every 2 seconds
  logMonitoringInterval = setInterval(() => {
    if (chatStatus.isRunning && !isShuttingDown) {
      checkForUpdates();
    } else {
      clearInterval(logMonitoringInterval);
      logMonitoringInterval = null;
    }
  }, 2000);
}

// Error handling
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\nShutting down server...');
  if (chatProcess) {
    chatProcess.kill('SIGTERM');
  }
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

// Start server
server.listen(PORT, '0.0.0.0', () => {
  console.log(`🌐 Web server running on http://0.0.0.0:${PORT}`);
  console.log(`🌐 Access via http://localhost:${PORT} or http://<your-ip-address>:${PORT}`);
  console.log(`🤖 Ollama host: ${OLLAMA_HOST}`);
});

// Bestehender Code bleibt unverändert
// ... existing code ...

// Neue API-Routen hinzufügen
app.post('/api/configure-models', (req, res) => {
  const { agent1Model, agent2Model, language, customTopic } = req.body;
  
  if (!agent1Model || !agent2Model) {
    return res.status(400).json({ error: 'Beide Agenten-Modelle müssen ausgewählt werden' });
  }
  
  // Umgebungsvariablen setzen
  process.env.AGENT1_MODEL = agent1Model;
  process.env.AGENT2_MODEL = agent2Model;
  process.env.CHAT_LANGUAGE = language || 'de';
  
  // Wenn ein benutzerdefiniertes Thema vorhanden ist, speichere es
  if (customTopic) {
    process.env.CUSTOM_TOPIC = customTopic;
  } else {
    // Löschen, falls vorhanden
    delete process.env.CUSTOM_TOPIC;
  }
  
  // In .env Datei schreiben für Persistenz
  const envPath = path.join(__dirname, '..', '.env');
  let envContent = '';
  
  try {
    if (fs.existsSync(envPath)) {
      envContent = fs.readFileSync(envPath, 'utf8');
    }
    
    // Update oder hinzufügen der Variablen
    const lines = envContent.split('\n');
    const updatedLines = [];
    let foundAgent1 = false, foundAgent2 = false, foundLanguage = false, foundTopic = false;
    
    lines.forEach(line => {
      if (line.startsWith('AGENT1_MODEL=')) {
        updatedLines.push(`AGENT1_MODEL=${agent1Model}`);
        foundAgent1 = true;
      } else if (line.startsWith('AGENT2_MODEL=')) {
        updatedLines.push(`AGENT2_MODEL=${agent2Model}`);
        foundAgent2 = true;
      } else if (line.startsWith('CHAT_LANGUAGE=')) {
        updatedLines.push(`CHAT_LANGUAGE=${language}`);
        foundLanguage = true;
      } else if (line.startsWith('CUSTOM_TOPIC=')) {
        if (customTopic) {
          updatedLines.push(`CUSTOM_TOPIC=${customTopic}`);
        }
        foundTopic = true;
      } else {
        updatedLines.push(line);
      }
    });
    
    if (!foundAgent1) updatedLines.push(`AGENT1_MODEL=${agent1Model}`);
    if (!foundAgent2) updatedLines.push(`AGENT2_MODEL=${agent2Model}`);
    if (!foundLanguage) updatedLines.push(`CHAT_LANGUAGE=${language}`);
    if (!foundTopic && customTopic) updatedLines.push(`CUSTOM_TOPIC=${customTopic}`);
    
    fs.writeFileSync(envPath, updatedLines.join('\n'));
    
  } catch (error) {
    console.error('Fehler beim Schreiben der .env Datei:', error);
  }
  
  res.json({ 
    message: 'Modelle erfolgreich konfiguriert',
    agent1Model,
    agent2Model,
    language,
    customTopic
  });
});

// API Route für das Löschen der Logs
app.post('/api/clear-logs', (req, res) => {
  const logDir = path.join(__dirname, '..', 'logs');
  const today = new Date().toISOString().split('T')[0].replace(/-/g, '');
  const logFile = path.join(logDir, `chat_${today}.json`);
  
  try {
    if (fs.existsSync(logFile)) {
      fs.writeFileSync(logFile, '[]'); // Leeres Array schreiben
    }
    
    // Chat Status zurücksetzen
    chatStatus.conversationLength = 0;
    chatStatus.lastMessage = null;
    
    // Alle Clients benachrichtigen
    io.emit('conversation-update', []);
    io.emit('chat-status', chatStatus);
    
    res.json({ message: 'Chat-Verlauf erfolgreich gelöscht' });
  } catch (error) {
    console.error('Fehler beim Löschen der Logs:', error);
    res.status(500).json({ error: 'Fehler beim Löschen der Logs' });
  }
});

// Improved graceful shutdown
function gracefulShutdown() {
  if (isShuttingDown) {
    return; // Prevent multiple shutdown attempts
  }
  
  isShuttingDown = true;
  console.log('\nShutting down server...');
  
  // Clear monitoring interval
  if (logMonitoringInterval) {
    clearInterval(logMonitoringInterval);
    logMonitoringInterval = null;
  }
  
  // Close all socket connections
  io.close(() => {
    console.log('Socket.IO connections closed');
  });
  
  // Kill chat process if running
  if (chatProcess) {
    chatProcess.removeAllListeners(); // Remove all listeners to prevent memory leaks
    chatProcess.kill('SIGTERM');
    chatProcess = null;
  }
  
  // Close HTTP server
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
  
  // Force exit after 5 seconds if graceful shutdown fails
  setTimeout(() => {
    console.log('Force closing server...');
    process.exit(1);
  }, 5000);
}

// Register shutdown handlers only once
process.once('SIGINT', gracefulShutdown);
process.once('SIGTERM', gracefulShutdown);

// Increase max listeners to prevent warnings
process.setMaxListeners(15);
server.setMaxListeners(15);