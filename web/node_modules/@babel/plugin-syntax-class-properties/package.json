{"name": "@babel/plugin-syntax-class-properties", "version": "7.12.13", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-class-properties"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-class-properties", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.12.13"}}