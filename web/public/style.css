/* =========================================
   AI Chat Bot CSS - Einheitliches Styling
   ========================================= */

/* --- 1. Grundlegende Resets und Variablen --- */
:root {
    /* Farbpalette */
    --primary-dark: #4a5568;
    --primary-light: #667eea;
    --secondary-dark: #764ba2;
    --text-dark: #333;
    --text-light: #718096;
    --text-muted: #666;
    
    /* Status-Farben */
    --success-bg: #c6f6d5;
    --success-text: #22543d;
    --warning-bg: #faf089;
    --warning-text: #744210;
    --error-bg: #fed7d7;
    --error-text: #742a2a;
    --info-bg: #bee3f8;
    --info-text: #2a4365;
    
    /* Agent-<PERSON><PERSON> */
    --alice-gradient-start: #ebf8ff;
    --alice-gradient-end: #bee3f8;
    --alice-accent: #3182ce;
    --bob-gradient-start: #fef5e7;
    --bob-gradient-end: #fed7aa;
    --bob-accent: #dd6b20;
    
    /* Allgemeine Elemente */
    --border-color: #e2e8f0;
    --card-bg: rgba(255, 255, 255, 0.95);
    --box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    --border-radius: 15px;
    --small-radius: 8px;
    --button-radius: 8px;
    --status-radius: 12px;
    
    /* Typografie */
    --font-size-xs: 0.75rem;   /* 12px */
    --font-size-sm: 0.875rem;  /* 14px */
    --font-size-base: 1rem;    /* 16px */
    --font-size-md: 1.125rem;  /* 18px */
    --font-size-lg: 1.25rem;   /* 20px */
    --font-size-xl: 1.5rem;    /* 24px */
    --font-size-2xl: 2rem;     /* 32px */
    --font-size-3xl: 2.5rem;   /* 40px */
    
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    
    --line-height-tight: 1.2;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.6;
    
    --letter-spacing-tight: -0.025em;
    --letter-spacing-normal: 0;
    --letter-spacing-wide: 0.025em;
    --letter-spacing-wider: 0.05em;
    --letter-spacing-widest: 0.1em;
    
    --padding-standard: 15px;
    --margin-standard: 15px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, var(--primary-light) 0%, var(--secondary-dark) 100%);
    min-height: 100vh;
    color: var(--text-dark);
    line-height: var(--line-height-normal);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
}

/* --- 2. Container und Layout --- */
.container {
    max-width: 1600px;
    margin: 0 auto;
    padding: 15px;
    display: grid;
    grid-template-columns: 30% 70%;
    grid-template-rows: auto auto 1fr;
    grid-gap: 15px;
    min-height: 100vh;
    grid-template-areas:
        "header header"
        "controls agents"
        "chat chat";
}

/* Responsive Layout für kleinere Bildschirme */
@media (max-width: 1200px) {
    .container {
        grid-template-columns: 1fr;
        grid-template-areas:
            "header"
            "controls"
            "agents"
            "chat";
    }
}

/* --- 3. Gemeinsame Kartenelemente --- */
.card-style, .status-panel, .main-control-panel, header, .chat-container, .agent-card {
    background: var(--card-bg);
    padding: var(--padding-standard);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    backdrop-filter: blur(10px);
}

/* --- 4. Header --- */
header {
    grid-area: header;
    text-align: center;
}

header h1 {
    font-size: var(--font-size-3xl);
    color: var(--primary-dark);
    margin-bottom: var(--margin-standard);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    font-weight: var(--font-weight-bold);
}

header h1 i {
    color: var(--primary-light);
}

.subtitle {
    font-size: var(--font-size-md);
    color: var(--text-light);
    font-weight: var(--font-weight-normal);
}

/* --- 5. Kontrollpanel --- */
.control-panel-wrapper {
    grid-area: controls;
    display: grid;
    grid-template-columns: 1fr 3fr;
    grid-gap: 20px;
}

/* Status Panel */
.status-panel {
    display: flex;
    flex-direction: column;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--margin-standard);
    padding-bottom: var(--margin-standard);
    border-bottom: 1px solid var(--border-color);
}

.status-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.label {
    font-weight: var(--font-weight-semibold);
    color: var(--primary-dark);
    font-size: var(--font-size-base);
}

.status-indicator {
    padding: 5px 12px;
    border-radius: 20px;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    text-transform: uppercase;
    letter-spacing: var(--letter-spacing-wide);
}

.status-indicator.connected {
    background: var(--success-bg);
    color: var(--success-text);
}

.status-indicator.running {
    background: var(--info-bg);
    color: var(--info-text);
}

.status-indicator.stopped {
    background: var(--error-bg);
    color: var(--error-text);
}

.status-indicator.checking {
    background: var(--warning-bg);
    color: var(--warning-text);
}

.status-value {
    font-weight: var(--font-weight-semibold);
    color: var(--primary-light);
    font-size: var(--font-size-md);
}

/* Main Control Panel - kompakter für neues Layout */
.main-control-panel {
    display: flex;
    flex-direction: column;
    gap: 15px;
    padding: 12px;
}

/* Control Buttons - kompakter */
.control-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 8px;
}

/* Config Panel - kompakter */
.config-panel {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* Model Selectors - kompakter */
.model-selectors {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.model-selector {
    margin-bottom: 0;
}

.model-selector label {
    font-size: var(--font-size-xs);
    margin-bottom: 3px;
}

.model-dropdown {
    font-size: var(--font-size-xs);
    padding: 6px 8px;
}

/* Topic Config - kompakter */
.topic-config {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

/* Flag Selector */
.flag-selector {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-top: 5px;
}

.flag-selector input[type="radio"] {
    display: none;
}

.flag-option {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 30px;
    border: 2px solid var(--border-color);
    border-radius: var(--small-radius);
    cursor: pointer;
    transition: all 0.2s ease;
    background: var(--card-bg);
}

.flag-option:hover {
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.flag-option .fi {
    font-size: 1.2em;
    border-radius: 2px;
}

.flag-selector input[type="radio"]:checked + .flag-option {
    border-color: var(--primary-color);
    background: var(--primary-light);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

#apply-config-btn {
    height: 35px;
    font-size: var(--font-size-xs);
}

/* --- 6. Buttons und Formularelemente --- */
.btn {
    padding: 10px;
    border: none;
    border-radius: var(--button-radius);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-decoration: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-light), var(--secondary-dark));
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #fc8181, #f56565);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, #f6ad55, #ed8936);
    color: white;
}

.btn-secondary {
    background: linear-gradient(135deg, #a0aec0, #718096);
    color: white;
}

.btn-small {
    padding: 6px 10px;
    font-size: var(--font-size-xs);
}

.btn-outline {
    background: transparent;
    border: 2px solid var(--border-color);
    color: var(--primary-dark);
}

.btn-outline.active {
    background: var(--primary-light);
    color: white;
    border-color: var(--primary-light);
}

.btn-text {
    text-transform: uppercase;
    font-weight: var(--font-weight-semibold);
    letter-spacing: var(--letter-spacing-wide);
}

/* Formularelemente */
select, input {
    padding: 8px 10px;
    border: 1px solid var(--border-color);
    border-radius: var(--small-radius);
    background: white;
    font-size: var(--font-size-sm);
    color: var(--primary-dark);
    transition: border-color 0.2s ease;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

select:focus, input:focus {
    outline: none;
    border-color: var(--primary-light);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

select option {
    display: flex;
    align-items: center;
    font-size: var(--font-size-sm);
    padding: 8px;
}

label, .label-text {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--primary-dark);
    margin-bottom: 5px;
    display: block;
}

/* --- 7. Agenten-Panel --- kompakter für neues Layout */
.agents-panel {
    grid-area: agents;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.agent-card {
    border: 1px solid var(--border-color);
    border-radius: var(--small-radius);
    padding: 12px;
    background-color: var(--card-bg);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    min-height: 120px;
    display: flex;
    flex-direction: column;
}

.agent-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.agent-header {
    display: flex;
    align-items: center;
    margin-bottom: var(--margin-standard);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: var(--padding-standard);
}

.agent-icon {
    font-size: var(--font-size-xl);
    margin-right: 10px;
}

.agent-name {
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-lg);
    color: var(--primary-dark);
}

.agent-info {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 12px;
}

.agent-model {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    font-weight: var(--font-weight-normal);
    line-height: 1.4;
    word-break: break-word;
}

/* Neue Styles für das Modell-Label */
.agent-model-label {
    font-weight: var(--font-weight-semibold);
    color: var(--primary-dark);
    display: block;
    margin-bottom: 2px;
}

/* Agent Capabilities Display */
.agent-capabilities {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    align-items: center;
    padding: 8px 0;
    margin: 5px 0;
}

.agent-capabilities span {
    background: rgba(0, 0, 0, 0.08);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    white-space: nowrap;
    border: 1px solid rgba(0, 0, 0, 0.1);
    color: var(--text-color);
}

.agent-capabilities .no-capabilities {
    color: var(--text-light);
    font-style: italic;
    background: transparent;
    border: none;
    padding: 0;
}

/* Dark theme adjustments */
.dark-theme .agent-capabilities span {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    color: var(--text-color);
}

.agent-status {
    padding: 6px 12px;
    border-radius: var(--status-radius);
    display: inline-block;
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    text-transform: uppercase;
    letter-spacing: var(--letter-spacing-wide);
    text-align: center;
    min-width: 80px;
    margin-top: 5px;
    align-self: flex-start;
}

.agent-ready {
    background-color: var(--success-bg);
    color: var(--success-text);
    box-shadow: 0 2px 4px rgba(34, 197, 94, 0.2);
}

.agent-not-ready {
    background-color: var(--error-bg);
    color: var(--error-text);
    box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2);
}

/* --- 8. Chat-Container --- 70% des Platzes */
.chat-container {
    grid-area: chat;
    display: flex;
    flex-direction: column;
    height: calc(100vh - 200px);
    min-height: 500px;
}

.chat-header {
    padding: var(--padding-standard);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-header h3 {
    color: var(--primary-dark);
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: var(--font-size-md);
    margin: 0;
    font-weight: var(--font-weight-semibold);
}

.header-text {
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-semibold);
}

.chat-controls {
    display: flex;
    gap: 10px;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: var(--padding-standard);
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* --- 9. Nachrichtenelemente --- */
.welcome-message {
    text-align: center;
    color: var(--text-light);
    padding: 40px var(--padding-standard);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.welcome-message i {
    font-size: var(--font-size-3xl);
    color: #cbd5e0;
}

.welcome-message p {
    font-size: var(--font-size-base);
    line-height: var(--line-height-relaxed);
}

.message {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: var(--padding-standard);
    border-radius: var(--small-radius);
    animation: fadeIn 0.5s ease;
}

.message.alice {
    background: linear-gradient(135deg, var(--alice-gradient-start), var(--alice-gradient-end));
    border-left: 4px solid var(--alice-accent);
}

.message.bob {
    background: linear-gradient(135deg, var(--bob-gradient-start), var(--bob-gradient-end));
    border-left: 4px solid var(--bob-accent);
}

.message-icon {
    font-size: var(--font-size-xl);
    margin-top: 2px;
}

.message-content {
    flex: 1;
}

.message-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
}

.message-author {
    font-weight: var(--font-weight-semibold);
    color: var(--primary-dark);
    font-size: var(--font-size-base);
}

.message-time {
    font-size: var(--font-size-xs);
    color: var(--text-light);
}

.message-text {
    color: var(--primary-dark);
    line-height: var(--line-height-relaxed);
    font-size: var(--font-size-base);
}

/* Performance-Informationen in Chat-Nachrichten */
.performance-info {
    font-size: 0.8em;
    color: #666;
    margin-left: 8px;
    padding: 2px 5px;
    border-radius: 4px;
    background-color: #f0f0f0;
    display: inline-block;
}

.dark-theme .performance-info {
    background-color: #333;
    color: #aaa;
}

/* Chat-Benachrichtigungen */
.chat-notification {
    padding: 15px;
    margin: 10px 0;
    border-radius: var(--small-radius);
    animation: slideIn 0.3s ease, fadeOut 0.5s ease 4.5s forwards;
    text-align: center;
}

.stop-notification {
    background: linear-gradient(135deg, #fee2e2, #fecaca);
    border: 1px solid #f87171;
    color: #dc2626;
}

.notification-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-weight: var(--font-weight-semibold);
}

.notification-content i {
    font-size: var(--font-size-lg);
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

/* Capability Badges */
.capabilities-badges {
    display: flex;
    gap: 5px;
    margin: 5px 0;
    flex-wrap: wrap;
}

.capability-badge {
    font-size: var(--font-size-xs);
    padding: 2px 6px;
    border-radius: var(--small-radius);
    background: rgba(0, 0, 0, 0.1);
    color: var(--text-muted);
    display: inline-flex;
    align-items: center;
    gap: 3px;
}

.capability-badge.thinking {
    background: rgba(147, 51, 234, 0.1);
    color: #7c3aed;
}

.capability-badge.vision {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.capability-badge.coding {
    background: rgba(34, 197, 94, 0.1);
    color: #22c55e;
}

.capability-badge.large-context {
    background: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
}

/* Thinking Content */
.thinking-toggle {
    background: none;
    border: 1px solid var(--border-color);
    border-radius: var(--small-radius);
    padding: 4px 8px;
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    cursor: pointer;
    transition: all 0.2s ease;
    margin-left: auto;
}

.thinking-toggle:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.thinking-toggle.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.thinking-content {
    margin-top: 10px;
    padding: 15px;
    background: rgba(147, 51, 234, 0.05);
    border: 1px solid rgba(147, 51, 234, 0.2);
    border-radius: var(--small-radius);
    animation: slideIn 0.3s ease;
}

.thinking-header {
    font-weight: var(--font-weight-semibold);
    color: #7c3aed;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: var(--font-size-sm);
}

.thinking-text {
    color: var(--text-color);
    line-height: 1.6;
    white-space: pre-wrap;
    font-family: 'Courier New', monospace;
    font-size: var(--font-size-sm);
    background: rgba(255, 255, 255, 0.5);
    padding: 10px;
    border-radius: var(--small-radius);
}

.dark-theme .thinking-text {
    background: rgba(0, 0, 0, 0.3);
}

/* Message header improvements */
.message-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
    flex-wrap: wrap;
}

/* Laufende Modelle Anzeige */
.status-section {
    margin-top: 20px;
    border-top: 1px solid var(--border-color);
    padding-top: 15px;
}

.status-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.status-section-header h4 {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--primary-dark);
    margin: 0;
}

.running-models-container {
    max-height: 200px;
    overflow-y: auto;
    font-size: var(--font-size-sm);
}

.no-models {
    color: var(--text-light);
    font-style: italic;
    padding: 5px 0;
}

.running-models-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.running-model-item {
    background: rgba(255, 255, 255, 0.5);
    border-radius: 6px;
    padding: 8px;
    border-left: 3px solid var(--primary-light);
}

.model-name {
    font-weight: var(--font-weight-semibold);
    margin-bottom: 4px;
    color: var(--primary-dark);
}

.model-details {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

.model-id, .model-cpu, .model-gpu, .model-memory {
    background: rgba(0, 0, 0, 0.05);
    padding: 2px 6px;
    border-radius: 4px;
}

.model-cpu {
    color: #2563eb;
}

.model-gpu {
    color: #7c3aed;
}

.model-memory {
    color: #db2777;
}

/* --- 10. Hilfskomponenten --- */
/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    background: white;
    padding: 30px;
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.loading-spinner i {
    font-size: var(--font-size-2xl);
    color: var(--primary-light);
    margin-bottom: var(--margin-standard);
}

.loading-spinner p {
    color: var(--primary-dark);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-base);
}

/* Flaggen-Icons */
.flag-icon {
    margin-right: 5px;
    display: inline-block;
    width: 20px;
    height: 15px;
    vertical-align: middle;
    position: relative;
    top: -1px;
}

/* Scrollbar Styling */
.chat-messages::-webkit-scrollbar {
    width: 8px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 4px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
}

/* Animationen */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* --- 11. Login-Seite --- */
.login-page {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(135deg, var(--primary-light) 0%, var(--secondary-dark) 100%);
}

.login-container {
    background: var(--card-bg);
    padding: 40px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    backdrop-filter: blur(10px);
    width: 100%;
    max-width: 400px;
    text-align: center;
}

.login-container h1 {
    color: var(--primary-dark);
    margin-bottom: 30px;
    font-size: var(--font-size-2xl);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-weight: var(--font-weight-bold);
}

.login-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-group {
    text-align: left;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: var(--font-weight-semibold);
    color: var(--primary-dark);
    font-size: var(--font-size-base);
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--small-radius);
    font-size: var(--font-size-base);
}

.login-button {
    background: linear-gradient(135deg, var(--primary-light), var(--secondary-dark));
    color: white;
    border: none;
    padding: 12px;
    border-radius: var(--small-radius);
    font-weight: var(--font-weight-semibold);
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: var(--letter-spacing-wide);
    font-size: var(--font-size-base);
}

.login-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* --- 12. Responsive Design --- */
@media (max-width: 1200px) {
    .agents-panel {
        flex-direction: row;
        gap: 15px;
    }

    .agent-card {
        flex: 1;
        min-height: 100px;
    }
}

@media (max-width: 768px) {
    :root {
        --font-size-base: 0.9rem;
        --font-size-lg: 1.1rem;
        --font-size-xl: 1.3rem;
        --font-size-2xl: 1.6rem;
        --font-size-3xl: 2rem;
        --padding-standard: 10px;
        --margin-standard: 10px;
    }
    
    .container {
        grid-gap: 10px;
        padding: 10px;
    }
    
    header {
        padding: var(--padding-standard);
    }
    
    .control-buttons {
        grid-template-columns: 1fr;
    }
    
    .agents-panel {
        grid-template-columns: 1fr;
    }
    
    .chat-container {
        height: calc(100vh - 600px);
        min-height: 300px;
    }
}