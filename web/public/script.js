// Global variables
let socket;
let autoScroll = true;
let chatStatus = { 
    isRunning: false,
    agents: {
        agent1: { model: '-', status: 'not running' },
        agent2: { model: '-', status: 'not running' }
    }
};
let conversationHistory = [];
let runningModels = [];

// Fallback-Methode für Socket.IO-Verbindungsprobleme
let useFallbackPolling = false;
let pollingInterval = null;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeSocket();
    initializeEventListeners();
    checkOllamaStatus();
    loadModels();
    loadAgentInfo();
    updateTopicPlaceholders();
    fetchRunningModels();
    updateUI();
});

// Initialize Socket.IO connection
function initializeSocket() {
    try {
        // Aktualisiere den Verbindungsstatus
        updateConnectionStatus('Connecting...', 'connecting');
        
        // Konfiguration für Socket.IO
        const socketOptions = {
            reconnectionAttempts: 5,
            reconnectionDelay: 3000,
            timeout: 10000,
            transports: ['websocket', 'polling']
        };
        
        // Verwende die aktuelle Host-URL
        const socketUrl = window.location.hostname;
        const socketPort = window.location.port || '3005';
        const socketProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        
        console.log(`Connecting to Socket.IO at ${socketUrl}:${socketPort}`);
        socket = io(`${socketUrl}:${socketPort}`, socketOptions);
        
        // Fallback-Erkennung einrichten
        setupFallbackDetection();
        
        socket.on('connect', function() {
            addLog('Connected to server', 'success');
            console.log('Socket.IO connected with ID:', socket.id);
            updateConnectionStatus('Connected', 'connected');
        });
        
        socket.on('connect_error', (error) => {
            console.error('Socket.IO connection error:', error);
            addLog(`Connection error: ${error.message}`, 'error');
            updateConnectionStatus('Connection Error', 'error');
        });
        
        socket.on('disconnect', function(reason) {
            addLog(`Disconnected from server: ${reason}`, 'error');
            console.log('Socket.IO disconnected, reason:', reason);
            updateConnectionStatus('Disconnected', 'stopped');
        });
        
        socket.on('reconnect_attempt', (attemptNumber) => {
            console.log(`Socket.IO reconnection attempt #${attemptNumber}`);
            addLog(`Reconnecting... (attempt ${attemptNumber})`, 'info');
            updateConnectionStatus(`Reconnecting (${attemptNumber})`, 'connecting');
        });
        
        socket.on('reconnect_failed', () => {
            console.error('Socket.IO reconnection failed');
            addLog('Reconnection failed. Please refresh the page.', 'error');
            updateConnectionStatus('Reconnect Failed', 'error');
        });
        
        socket.on('chat-status', function(status) {
            chatStatus = status;
            updateChatStatus();
            updateAgentCards();
        });
        
        socket.on('conversation-history', function(history) {
            conversationHistory = history;
            displayConversation();
        });
        
        socket.on('conversation-update', function(history) {
            conversationHistory = history;
            displayConversation();
            if (autoScroll) {
                scrollToBottom();
            }
        });
        
        socket.on('chat-output', function(data) {
            const logType = data.type === 'stderr' ? 'error' : 'info';
            addLog(data.data.trim(), logType);
        });
        
        socket.on('agents-update', function(agents) {
            console.log('Received agents update:', agents);
            if (!agents) {
                console.warn('Received empty agents update');
                return;
            }

            // Aktualisiere chatStatus.agents mit den neuen Daten
            chatStatus.agents = agents;

            // Aktualisiere die UI
            updateAgentCards(agents);
        });

        socket.on('chat-stopped', function(data) {
            console.log('Chat stopped:', data);
            addLog(`🛑 ${data.message}`, 'info');

            // Zeige visuelles Feedback
            showChatStoppedNotification(data.message);
        });
    } catch (error) {
        console.error('Error initializing Socket.IO:', error);
        addLog(`Failed to connect: ${error.message}`, 'error');
        updateConnectionStatus('Connection Failed', 'error');
        
        // Aktiviere Fallback bei Socket.IO-Initialisierungsfehler
        enableFallbackPolling();
    }
}

// Initialize event listeners
function initializeEventListeners() {
    // Control buttons
    const startBtn = document.getElementById('start-btn');
    const stopBtn = document.getElementById('stop-btn');
    const restartBtn = document.getElementById('restart-btn');
    const clearBtn = document.getElementById('clear-btn');
    const autoScrollBtn = document.getElementById('auto-scroll-btn');
    const exportBtn = document.getElementById('export-btn');
    const configBtn = document.getElementById('apply-config-btn');
    const refreshModelsBtn = document.getElementById('refresh-models-btn');
    
    if (startBtn) startBtn.addEventListener('click', startChat);
    if (stopBtn) stopBtn.addEventListener('click', stopChat);
    if (restartBtn) restartBtn.addEventListener('click', restartChat);
    if (clearBtn) clearBtn.addEventListener('click', clearChat);
    if (autoScrollBtn) autoScrollBtn.addEventListener('click', toggleAutoScroll);
    if (exportBtn) exportBtn.addEventListener('click', exportConversation);
    if (configBtn) configBtn.addEventListener('click', applyConfiguration);
    if (refreshModelsBtn) refreshModelsBtn.addEventListener('click', fetchRunningModels);
    
    // Themenauswahl-Interaktion
    const topicSelector = document.getElementById('chat-topic');
    const customTopicContainer = document.getElementById('custom-topic-container');
    
    if (topicSelector && customTopicContainer) {
        topicSelector.addEventListener('change', function() {
            if (this.value === 'custom') {
                customTopicContainer.style.display = 'flex';
            } else {
                customTopicContainer.style.display = 'none';
            }
        });
    }
    
    // Sprachauswahl-Interaktion - Bei Änderung der Sprache die Thementexte aktualisieren
    const languageSelect = document.getElementById('chat-language');
    if (languageSelect) {
        languageSelect.addEventListener('change', function() {
            updateTopicPlaceholders();
        });
    }
}

// Verbesserte Funktion, um die Beschreibungen und Texte basierend auf der Sprache zu aktualisieren
function updateTopicPlaceholders() {
    const language = document.getElementById('chat-language').value;
    
    // UI-Übersetzungen für alle Texte
    const translations = {
        'de': {
            // Statuspanel
            'status_ollama': 'Ollama:',
            'status_chat': 'Chat:',
            'status_messages': 'Nachrichten:',
            'status_connection': 'Verbindung:',
            'status_checking': 'PRÜFE...',
            'status_connected': 'VERBUNDEN',
            'status_disconnected': 'GETRENNT',
            'status_error': 'FEHLER',
            'status_running': 'LÄUFT',
            'status_stopped': 'GESTOPPT',
            
            // Buttons
            'btn_start': 'START',
            'btn_stop': 'STOP',
            'btn_restart': 'NEUSTART',
            'btn_clear': 'LÖSCHEN',
            'btn_apply': 'ÜBERNEHMEN',
            'btn_auto': 'AUTO',
            'btn_export': 'EXPORT',
            
            // Labels
            'label_language': 'Sprache:',
            'label_topic': 'Thema:',
            'label_model': 'Modell:',
            
            // Topics
            'topic_random': 'Zufälliges Thema',
            'topic_ai': 'Künstliche Intelligenz',
            'topic_travel': 'Reisen',
            'topic_learning': 'Lernen und Bildung',
            'topic_space': 'Weltraum und andere Planeten',
            'topic_climate': 'Klimawandel',
            'topic_friendship': 'Freundschaft',
            'topic_superpower': 'Superkräfte',
            'topic_creativity': 'Kreativität',
            'topic_communication': 'Kommunikation',
            'topic_custom': 'Eigenes Thema...',
            
            // Placeholders
            'placeholder_topic': 'Gib ein eigenes Gesprächsthema ein...',
            
            // Headers
            'header_conversation': 'Live Unterhaltung',
            
            // Agent info
            'agent_model': 'Modell:',
            'agent_status_ready': 'BEREIT',
            'agent_status_not_ready': 'NICHT BEREIT',
            
            // Messages
            'welcome_message': 'Willkommen beim KI Chat Bot! Klicken Sie auf "START", um eine Unterhaltung zwischen zwei KI-Agenten zu beginnen.',
            'loading_message': 'Verarbeite...'
        },
        'en': {
            // Status panel
            'status_ollama': 'Ollama:',
            'status_chat': 'Chat:',
            'status_messages': 'Messages:',
            'status_connection': 'Connection:',
            'status_checking': 'CHECKING...',
            'status_connected': 'CONNECTED',
            'status_disconnected': 'DISCONNECTED',
            'status_error': 'ERROR',
            'status_running': 'RUNNING',
            'status_stopped': 'STOPPED',
            
            // Buttons
            'btn_start': 'START',
            'btn_stop': 'STOP',
            'btn_restart': 'RESTART',
            'btn_clear': 'CLEAR',
            'btn_apply': 'APPLY',
            'btn_auto': 'AUTO',
            'btn_export': 'EXPORT',
            
            // Labels
            'label_language': 'Language:',
            'label_topic': 'Topic:',
            'label_model': 'Model:',
            
            // Topics
            'topic_random': 'Random Topic',
            'topic_ai': 'Artificial Intelligence',
            'topic_travel': 'Travel',
            'topic_learning': 'Learning and Education',
            'topic_space': 'Space and Other Planets',
            'topic_climate': 'Climate Change',
            'topic_friendship': 'Friendship',
            'topic_superpower': 'Superpowers',
            'topic_creativity': 'Creativity',
            'topic_communication': 'Communication',
            'topic_custom': 'Custom topic...',
            
            // Placeholders
            'placeholder_topic': 'Enter your own conversation topic...',
            
            // Headers
            'header_conversation': 'Live Conversation',
            
            // Agent info
            'agent_model': 'Model:',
            'agent_status_ready': 'READY',
            'agent_status_not_ready': 'NOT READY',
            
            // Messages
            'welcome_message': 'Welcome to the AI Chat Bot! Click "START" to begin a conversation between two AI agents.',
            'loading_message': 'Processing...'
        }
    };
    
    // Aktuelle Sprache festlegen
    const currentTranslation = translations[language] || translations['de'];
    
    // UI-Elemente aktualisieren
    
    // Status Panel
    updateElementText('.status-panel .label', 0, currentTranslation.status_ollama);
    updateElementText('.status-panel .label', 1, currentTranslation.status_chat);
    updateElementText('.status-panel .label', 2, currentTranslation.status_messages);
    updateElementText('.status-panel .label', 3, currentTranslation.status_connection);
    
    // Buttons
    updateElementText('#start-btn .btn-text', null, currentTranslation.btn_start);
    updateElementText('#stop-btn .btn-text', null, currentTranslation.btn_stop);
    updateElementText('#restart-btn .btn-text', null, currentTranslation.btn_restart);
    updateElementText('#clear-btn .btn-text', null, currentTranslation.btn_clear);
    updateElementText('#apply-config-btn .btn-text', null, currentTranslation.btn_apply);
    updateElementText('#auto-scroll-btn .btn-text', null, currentTranslation.btn_auto);
    updateElementText('#export-btn .btn-text', null, currentTranslation.btn_export);
    
    // Labels
    updateElementText('.language-selector .label-text', null, currentTranslation.label_language);
    updateElementText('.topic-selector .label-text', null, currentTranslation.label_topic);
    
    // Nachrichten
    updateElementText('#welcome-text', null, currentTranslation.welcome_message);
    updateElementText('#loading-text', null, currentTranslation.loading_message);
    
    // Header
    updateElementText('.chat-header .header-text', null, currentTranslation.header_conversation);
    
    // Topic-Dropdown
    const topicSelect = document.getElementById('chat-topic');
    if (topicSelect) {
        const options = {
            '': currentTranslation.topic_random,
            'ai': currentTranslation.topic_ai,
            'travel': currentTranslation.topic_travel,
            'learning': currentTranslation.topic_learning,
            'space': currentTranslation.topic_space,
            'climate': currentTranslation.topic_climate,
            'friendship': currentTranslation.topic_friendship,
            'superpower': currentTranslation.topic_superpower,
            'creativity': currentTranslation.topic_creativity,
            'communication': currentTranslation.topic_communication,
            'custom': currentTranslation.topic_custom
        };
        
        Array.from(topicSelect.options).forEach(option => {
            const value = option.value;
            if (options[value]) {
                option.textContent = options[value];
            }
        });
    }
    
    // Custom Topic Placeholder
    const customTopicInput = document.getElementById('custom-topic');
    if (customTopicInput) {
        customTopicInput.placeholder = currentTranslation.placeholder_topic;
    }
}

// Hilfsfunktion zum Aktualisieren von Texten
function updateElementText(selector, index, text) {
    if (!text) return;
    
    const elements = document.querySelectorAll(selector);
    if (elements.length === 0) return;
    
    if (index !== null && index >= 0 && index < elements.length) {
        elements[index].textContent = text;
    } else if (elements.length > 0) {
        // Wenn kein Index angegeben ist, aktualisieren wir das erste Element
        elements[0].textContent = text;
    }
}

// Check Ollama server status
async function checkOllamaStatus() {
    try {
        console.log('Checking Ollama status...');
        const response = await fetch('/api/ollama/test');
        const data = await response.json();
        console.log('Ollama status response:', data);
        
        const statusElement = document.getElementById('ollama-status');
        if (statusElement) {
            if (data.status === 'connected') {
                statusElement.textContent = 'Connected';
                statusElement.className = 'status-indicator connected';
                addLog(`Connected to Ollama (${data.models} models available)`, 'success');
                if (data.modelList) {
                    console.log('Available models:', data.modelList);
                }
            } else {
                statusElement.textContent = 'Error';
                statusElement.className = 'status-indicator stopped';
                addLog(`Ollama connection failed: ${data.error} (${data.details || 'No details'})`, 'error');
            }
        }
    } catch (error) {
        console.error('Failed to check Ollama status:', error);
        const statusElement = document.getElementById('ollama-status');
        if (statusElement) {
            statusElement.textContent = 'Error';
            statusElement.className = 'status-indicator stopped';
            addLog(`Failed to check Ollama status: ${error.message}`, 'error');
        }
    }
}

// Update agent cards with latest agent information
function updateAgentCards(agents) {
    // Wenn agents nicht explizit übergeben wurde, verwende chatStatus.agents
    agents = agents || chatStatus.agents;
    
    if (!agents) {
        console.warn('No agent information available to update cards');
        return;
    }
    
    console.log('Updating agent cards with:', agents);
    
    // Aktuelle Sprache abrufen
    const language = document.getElementById('chat-language')?.value || 'de';
    const isEnglish = language === 'en';
    
    // Texte basierend auf Sprache
    const modelLabel = isEnglish ? 'Model: ' : 'Modell: ';
    const readyText = isEnglish ? 'READY' : 'BEREIT';
    const notReadyText = isEnglish ? 'NOT READY' : 'NICHT BEREIT';
    
    // Agent 1 Card (Alice)
    const agent1Card = document.getElementById('agent1-card');
    if (agent1Card && agents.agent1) {
        const agent1 = agents.agent1;

        // Aktualisiere Model-Info mit formatiertem Label
        const modelElement = agent1Card.querySelector('.agent-model');
        if (modelElement) {
            modelElement.innerHTML = `<span class="agent-model-label">${modelLabel}</span>${agent1.model || '-'}`;
        }

        // Aktualisiere Capabilities
        const capabilitiesElement = agent1Card.querySelector('.agent-capabilities');
        if (capabilitiesElement && agent1.capabilities) {
            const capabilities = [];
            if (agent1.capabilities.thinking) capabilities.push('🧠 Thinking');
            if (agent1.capabilities.vision) capabilities.push('👁️ Vision');
            if (agent1.capabilities.coding) capabilities.push('💻 Coding');
            if (agent1.capabilities.large_context) capabilities.push('📚 Large Context');

            capabilitiesElement.innerHTML = capabilities.length > 0
                ? capabilities.join(' ')
                : '<span class="no-capabilities">Standard</span>';
        }

        // Aktualisiere Status
        const statusElement = agent1Card.querySelector('.agent-status');
        if (statusElement) {
            if (agent1.status === 'ready') {
                statusElement.textContent = readyText;
                statusElement.className = 'agent-status agent-ready';
            } else {
                statusElement.textContent = notReadyText;
                statusElement.className = 'agent-status agent-not-ready';
            }
        }
    }
    
    // Agent 2 Card (Bob)
    const agent2Card = document.getElementById('agent2-card');
    if (agent2Card && agents.agent2) {
        const agent2 = agents.agent2;

        // Aktualisiere Model-Info mit formatiertem Label
        const modelElement = agent2Card.querySelector('.agent-model');
        if (modelElement) {
            modelElement.innerHTML = `<span class="agent-model-label">${modelLabel}</span>${agent2.model || '-'}`;
        }

        // Aktualisiere Capabilities
        const capabilitiesElement = agent2Card.querySelector('.agent-capabilities');
        if (capabilitiesElement && agent2.capabilities) {
            const capabilities = [];
            if (agent2.capabilities.thinking) capabilities.push('🧠 Thinking');
            if (agent2.capabilities.vision) capabilities.push('👁️ Vision');
            if (agent2.capabilities.coding) capabilities.push('💻 Coding');
            if (agent2.capabilities.large_context) capabilities.push('📚 Large Context');

            capabilitiesElement.innerHTML = capabilities.length > 0
                ? capabilities.join(' ')
                : '<span class="no-capabilities">Standard</span>';
        }

        // Aktualisiere Status
        const statusElement = agent2Card.querySelector('.agent-status');
        if (statusElement) {
            if (agent2.status === 'ready') {
                statusElement.textContent = readyText;
                statusElement.className = 'agent-status agent-ready';
            } else {
                statusElement.textContent = notReadyText;
                statusElement.className = 'agent-status agent-not-ready';
            }
        }
    }
}

// Detect model capabilities (client-side version)
function detectModelCapabilities(modelName) {
    const model = modelName.toLowerCase();
    const capabilities = {
        thinking: false,
        vision: false,
        coding: false,
        large_context: false
    };

    // Thinking capability detection
    const thinkingModels = ['deepseek-r1', 'qwen2.5-coder', 'o1-preview', 'o1-mini'];
    capabilities.thinking = thinkingModels.some(tm => model.includes(tm));

    // Vision capability detection
    const visionModels = ['qwen2.5vl', 'llava', 'bakllava', 'moondream', 'vision'];
    capabilities.vision = visionModels.some(vm => model.includes(vm));

    // Coding capability detection
    const codingModels = ['coder', 'code', 'devstral', 'deepseek-coder', 'starcoder', 'codellama'];
    capabilities.coding = codingModels.some(cm => model.includes(cm));

    // Large context capability detection
    const largeContextIndicators = ['32b', '27b', '24b', '70b', 'qwen2.5', 'deepseek'];
    capabilities.large_context = largeContextIndicators.some(lci => model.includes(lci));

    return capabilities;
}

// Create capability badges for model selection
function createCapabilityBadges(capabilities) {
    const badges = [];
    if (capabilities.thinking) badges.push('🧠');
    if (capabilities.vision) badges.push('👁️');
    if (capabilities.coding) badges.push('💻');
    if (capabilities.large_context) badges.push('📚');
    return badges.length > 0 ? ` (${badges.join(' ')})` : '';
}

// Load available models
function populateModelDropdowns(models) {
    const agent1Select = document.getElementById('agent1-model');
    const agent2Select = document.getElementById('agent2-model');

    if (!agent1Select || !agent2Select) {
        console.error('Model dropdown elements not found');
        return;
    }

    console.log('Populating model dropdowns with:', models);

    // Clear existing options except first
    agent1Select.innerHTML = '<option value="">Modell auswählen...</option>';
    agent2Select.innerHTML = '<option value="">Modell auswählen...</option>';

    if (models && models.length > 0) {
        models.forEach(model => {
            // Wenn model ein String ist, verwende es direkt, sonst verwende model.name
            const modelName = typeof model === 'string' ? model : model.name;

            // Detect capabilities and create display text
            const capabilities = detectModelCapabilities(modelName);
            const capabilityBadges = createCapabilityBadges(capabilities);
            const displayText = `${modelName}${capabilityBadges}`;

            const option1 = document.createElement('option');
            option1.value = modelName;
            option1.textContent = displayText;
            option1.setAttribute('data-capabilities', JSON.stringify(capabilities));
            agent1Select.appendChild(option1);

            const option2 = document.createElement('option');
            option2.value = modelName;
            option2.textContent = displayText;
            option2.setAttribute('data-capabilities', JSON.stringify(capabilities));
            agent2Select.appendChild(option2);
        });

        // Standardauswahl setzen
        if (models.length >= 2) {
            agent1Select.value = typeof models[0] === 'string' ? models[0] : models[0].name;
            agent2Select.value = typeof models[1] === 'string' ? models[1] : models[1].name;
        } else if (models.length === 1) {
            agent1Select.value = typeof models[0] === 'string' ? models[0] : models[0].name;
            agent2Select.value = typeof models[0] === 'string' ? models[0] : models[0].name;
        }
    }
}

// loadModels() Funktion erweitern
async function loadModels() {
    try {
        console.log('Loading models from API...');
        const response = await fetch('/api/ollama/models');
        const data = await response.json();
        console.log('API model data:', data);
        
        if (data.models && data.models.length > 0) {
            // Dropdown-Menüs befüllen mit den vollständigen Modelldaten
            populateModelDropdowns(data.models);
        } else if (data.modelList && data.modelList.length > 0) {
            // Alternative: Verwende modelList, falls vorhanden (von /api/ollama/test)
            console.log('Using modelList as fallback:', data.modelList);
            populateModelDropdowns(data.modelList);
        } else {
            console.warn('No models available from API');
            
            // Fallback: Prüfe direkt den Ollama-Status für Modelle
            await checkOllamaStatusAndLoadModels();
        }
    } catch (error) {
        console.error('Error loading models:', error);
        addLog(`Fehler beim Laden der Modelle: ${error.message}`, 'error');
        
        // Fallback-Versuch bei Fehler
        await checkOllamaStatusAndLoadModels();
    }
}

// Neue Funktion als Fallback für die Modellladung
async function checkOllamaStatusAndLoadModels() {
    try {
        console.log('Attempting to load models from Ollama status...');
        const response = await fetch('/api/ollama/test');
        const data = await response.json();
        
        if (data.modelList && data.modelList.length > 0) {
            console.log('Loading models from Ollama status response:', data.modelList);
            populateModelDropdowns(data.modelList);
        } else {
            console.error('Failed to get models from Ollama status');
            addLog('Keine Modelle verfügbar. Bitte Ollama-Server überprüfen.', 'error');
        }
    } catch (error) {
        console.error('Error in Ollama status fallback:', error);
        addLog('Verbindung zum Ollama-Server fehlgeschlagen.', 'error');
    }
}

// Konfiguration anwenden
async function applyConfiguration() {
    const agent1Model = document.getElementById('agent1-model');
    const agent2Model = document.getElementById('agent2-model');
    const languageRadios = document.querySelectorAll('input[name="language"]');
    const topicSelect = document.getElementById('chat-topic');
    const customTopic = document.getElementById('custom-topic');

    if (!agent1Model || !agent2Model) {
        addLog('Modell-Dropdown-Elemente nicht gefunden', 'error');
        return;
    }

    const model1 = agent1Model.value;
    const model2 = agent2Model.value;

    // Get selected language from radio buttons
    let language = 'de'; // default
    languageRadios.forEach(radio => {
        if (radio.checked) {
            language = radio.value;
        }
    });
    
    // Thema auslesen
    let topic = '';
    if (topicSelect) {
        if (topicSelect.value === 'custom' && customTopic) {
            // Benutzerdefiniertes Thema
            topic = customTopic.value.trim();
        } else if (topicSelect.value) {
            // Vordefiniertes Thema
            topic = getPresetTopic(topicSelect.value);
        }
    }
    
    if (!model1 || !model2) {
        addLog('Bitte wähle Modelle für beide Agenten aus', 'error');
        return;
    }
    
    try {
        showLoading(true);
        
        const response = await fetch('/api/configure-models', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ 
                agent1Model: model1, 
                agent2Model: model2,
                language: language,
                customTopic: topic 
            })
        });
        
        const data = await response.json();
        
        if (response.ok) {
            let message = `Konfiguration gespeichert: Agent 1=${model1}, Agent 2=${model2}`;
            if (topic) {
                message += `, Thema="${topic}"`;
            }
            addLog(message, 'success');
        } else {
            addLog(`Fehler beim Speichern der Konfiguration: ${data.error}`, 'error');
        }
    } catch (error) {
        addLog(`Fehler bei der Konfiguration: ${error.message}`, 'error');
    } finally {
        showLoading(false);
    }
}

// Clear chat display
function clearChatDisplay() {
    const chatMessages = document.getElementById('chat-messages');
    if (!chatMessages) return;
    
    chatMessages.innerHTML = `
        <div class="welcome-message">
            <i class="fas fa-info-circle"></i>
            <p>Welcome to the AI Chat Bot! Click "Start Chat" to begin a conversation between two AI agents.</p>
        </div>
    `;
}

// Clear chat
function clearChat() {
    clearChatDisplay();
    conversationHistory = [];
    addLog('Chat display cleared', 'info');
}

// Add log entry - vereinfachte Version ohne UI-Element
function addLog(message, type = 'info') {
    // Nur Konsolenausgabe, kein DOM-Element mehr
    const timestamp = new Date().toLocaleTimeString();
    console.log(`[${timestamp}] [${type}] ${message}`);
}

// Zeige Chat-Stopp-Benachrichtigung
function showChatStoppedNotification(message) {
    // Erstelle Benachrichtigung
    const notification = document.createElement('div');
    notification.className = 'chat-notification stop-notification';
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-stop-circle"></i>
            <span>${message}</span>
        </div>
    `;

    // Füge zur Chat-Anzeige hinzu
    const chatMessages = document.getElementById('chat-messages');
    if (chatMessages) {
        chatMessages.appendChild(notification);

        // Auto-scroll wenn aktiviert
        if (autoScroll) {
            scrollToBottom();
        }

        // Entferne nach 5 Sekunden
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
}

// Start chat - mit Option für Thema
async function startChat() {
    try {
        showLoading(true);
        
        // Prüfen ob ein eigenes Thema angegeben wurde
        const topicSelect = document.getElementById('chat-topic');
        const customTopic = document.getElementById('custom-topic');
        
        let chatTopic = '';
        if (topicSelect) {
            if (topicSelect.value === 'custom' && customTopic && customTopic.value.trim()) {
                // Benutzerdefiniertes Thema
                chatTopic = customTopic.value.trim();
            } else if (topicSelect.value) {
                // Vordefiniertes Thema aus der Auswahl
                chatTopic = getPresetTopic(topicSelect.value);
            }
        }
        
        const requestBody = chatTopic ? { customTopic: chatTopic } : {};
        
        const response = await fetch('/api/start-chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });
        
        const data = await response.json();
        
        if (response.ok) {
            if (chatTopic) {
                addLog(`Chat gestartet mit Thema: "${chatTopic}"`, 'success');
            } else {
                addLog('Chat gestartet mit zufälligem Thema', 'success');
            }
        } else {
            addLog(`Fehler beim Starten des Chats: ${data.error}`, 'error');
        }
    } catch (error) {
        addLog(`Fehler beim Starten des Chats: ${error.message}`, 'error');
    } finally {
        showLoading(false);
    }
}

// Hilfsfunktion: Voreingestelltes Thema basierend auf Schlüssel abrufen
function getPresetTopic(key) {
    const presetTopics = {
        'ai': 'Was denkst du über die Zukunft der künstlichen Intelligenz?',
        'travel': 'Wenn du überall auf der Welt hinreisen könntest, wohin würdest du gehen und warum?',
        'learning': 'Was ist das Interessanteste, was du kürzlich gelernt hast?',
        'space': 'Glaubst du, dass Menschen jemals auf anderen Planeten leben werden?',
        'climate': 'Wie können wir deiner Meinung nach den Klimawandel lösen?',
        'friendship': 'Was macht einen guten Freund aus?',
        'superpower': 'Wenn du eine Superkraft haben könntest, welche wäre das?',
        'creativity': 'Was ist deine Meinung zur Bedeutung von Kreativität?',
        'communication': 'Wie wird sich die Kommunikation deiner Meinung nach in Zukunft entwickeln?'
    };
    
    // English versions for 'en' language
    const presetTopicsEn = {
        'ai': 'What do you think about the future of artificial intelligence?',
        'travel': 'If you could travel anywhere in the world, where would you go and why?',
        'learning': 'What\'s the most interesting thing you\'ve learned recently?',
        'space': 'Do you think humans will ever live on other planets?',
        'climate': 'How do you think we can solve climate change?',
        'friendship': 'What makes a good friend?',
        'superpower': 'If you could have any superpower, what would it be?',
        'creativity': 'What\'s your opinion on the importance of creativity?',
        'communication': 'How do you think communication will evolve in the future?'
    };
    
    // Prüfe die aktuelle Spracheinstellung
    const languageSelect = document.getElementById('chat-language');
    const isEnglish = languageSelect && languageSelect.value === 'en';
    
    // Entsprechendes Topic zurückgeben basierend auf Sprache
    if (isEnglish) {
        return presetTopicsEn[key] || '';
    } else {
        return presetTopics[key] || '';
    }
}

// Stop chat
async function stopChat() {
    try {
        showLoading(true);
        
        const response = await fetch('/api/stop-chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const data = await response.json();
        
        if (response.ok) {
            addLog('Chat stopped successfully', 'success');
        } else {
            addLog(`Failed to stop chat: ${data.error}`, 'error');
        }
    } catch (error) {
        addLog(`Error stopping chat: ${error.message}`, 'error');
    } finally {
        showLoading(false);
    }
}

// Restart chat
async function restartChat() {
    try {
        showLoading(true);
        
        const response = await fetch('/api/restart-chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const data = await response.json();
        
        if (response.ok) {
            addLog('Chat restarted successfully', 'success');
            clearChatDisplay();
        } else {
            addLog(`Failed to restart chat: ${data.error}`, 'error');
        }
    } catch (error) {
        addLog(`Error restarting chat: ${error.message}`, 'error');
    } finally {
        showLoading(false);
    }
}

// Toggle auto-scroll
function toggleAutoScroll() {
    autoScroll = !autoScroll;
    const btn = document.getElementById('auto-scroll-btn');
    if (!btn) return;
    
    if (autoScroll) {
        btn.classList.add('active');
        scrollToBottom();
    } else {
        btn.classList.remove('active');
    }
}

// Export conversation
function exportConversation() {
    if (conversationHistory.length === 0) {
        addLog('No conversation to export', 'info');
        return;
    }
    
    const dataStr = JSON.stringify(conversationHistory, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `ai-chat-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    
    addLog('Conversation exported', 'success');
}

// Update UI based on chat status
function updateUI() {
    updateChatStatus();
    updateAgentCards();
    
    // Update model dropdowns if models are already loaded
    const agent1Select = document.getElementById('agent1-model');
    const agent2Select = document.getElementById('agent2-model');
    
    if (agent1Select && agent2Select) {
        if (chatStatus.agents && chatStatus.agents.agent1 && chatStatus.agents.agent1.model) {
            for (let i = 0; i < agent1Select.options.length; i++) {
                if (agent1Select.options[i].value === chatStatus.agents.agent1.model) {
                    agent1Select.selectedIndex = i;
                    break;
                }
            }
        }
        
        if (chatStatus.agents && chatStatus.agents.agent2 && chatStatus.agents.agent2.model) {
            for (let i = 0; i < agent2Select.options.length; i++) {
                if (agent2Select.options[i].value === chatStatus.agents.agent2.model) {
                    agent2Select.selectedIndex = i;
                    break;
                }
            }
        }
    }
}

// Update chat status display
function updateChatStatus() {
    const statusElement = document.getElementById('chat-status');
    const messageCountElement = document.getElementById('message-count');
    const startBtn = document.getElementById('start-btn');
    const stopBtn = document.getElementById('stop-btn');
    
    if (!statusElement || !messageCountElement || !startBtn || !stopBtn) return;
    
    // Sprachabhängige Texte
    const language = document.getElementById('chat-language')?.value || 'de';
    const runningText = language === 'en' ? 'RUNNING' : 'LÄUFT';
    const stoppedText = language === 'en' ? 'STOPPED' : 'GESTOPPT';
    
    if (chatStatus.isRunning) {
        statusElement.textContent = runningText;
        statusElement.className = 'status-indicator running';
        startBtn.disabled = true;
        stopBtn.disabled = false;
    } else {
        statusElement.textContent = stoppedText;
        statusElement.className = 'status-indicator stopped';
        startBtn.disabled = false;
        stopBtn.disabled = true;
    }
    
    messageCountElement.textContent = chatStatus.conversationLength || 0;
}

// Display conversation
function displayConversation() {
    let chatMessages = document.getElementById('chat-messages');
    
    // Falls der Chat-Container nicht existiert, erstelle ihn
    if (!chatMessages) {
        console.warn('Chat-messages container not found, creating one');
        const mainContainer = document.querySelector('.chat-container') || document.querySelector('.main-container') || document.body;
        chatMessages = document.createElement('div');
        chatMessages.id = 'chat-messages';
        chatMessages.className = 'chat-messages';
        mainContainer.appendChild(chatMessages);
    }
    
    if (conversationHistory.length === 0) {
        clearChatDisplay();
        return;
    }
    
    chatMessages.innerHTML = '';
    
    try {
        conversationHistory.forEach(message => {
            if (!message || !message.agent) {
                console.warn('Invalid message in conversation history:', message);
                return;
            }
            const messageElement = createMessageElement(message);
            chatMessages.appendChild(messageElement);
        });
    } catch (error) {
        console.error('Error displaying conversation:', error);
        chatMessages.innerHTML = '<div class="error-message">Fehler beim Anzeigen der Konversation</div>';
    }
    
    if (autoScroll) {
        scrollToBottom();
    }
}

// Create message element
function createMessageElement(message) {
    if (!message || typeof message !== 'object') {
        console.warn('Invalid message object:', message);
        const errorDiv = document.createElement('div');
        errorDiv.className = 'message error';
        errorDiv.innerHTML = '<div class="message-content"><div class="message-text">Ungültige Nachricht</div></div>';
        return errorDiv;
    }
    
    const messageDiv = document.createElement('div');
    const agentName = message.agent || 'Unknown';
    const agentClass = agentName.toLowerCase();
    messageDiv.className = `message ${agentClass}`;
    
    // Icon basierend auf dem Agenten
    let icon = '❓';
    if (agentName === 'Alice' || agentName.toLowerCase().includes('agent1')) {
        icon = '🔵';
    } else if (agentName === 'Bob' || agentName.toLowerCase().includes('agent2')) {
        icon = '🔴';
    }
    
    // Sicherer Timestamp-Zugriff
    let timestamp = 'Unknown time';
    try {
        if (message.timestamp) {
            timestamp = new Date(message.timestamp).toLocaleTimeString();
        }
    } catch (e) {
        console.warn('Error parsing timestamp:', e);
    }
    
    // Performance-Informationen hinzufügen, wenn vorhanden
    let performanceInfo = '';
    if (message.generation_time) {
        performanceInfo += `<span class="performance-info">⏱️ ${message.generation_time}s</span>`;
    }

    if (message.token_info && message.token_info.total_tokens) {
        performanceInfo += `<span class="performance-info">🔢 ${message.token_info.total_tokens} tokens</span>`;
    }

    // Capabilities badges
    let capabilitiesBadges = '';
    if (message.capabilities) {
        const badges = [];
        if (message.capabilities.thinking) badges.push('<span class="capability-badge thinking">🧠</span>');
        if (message.capabilities.vision) badges.push('<span class="capability-badge vision">👁️</span>');
        if (message.capabilities.coding) badges.push('<span class="capability-badge coding">💻</span>');
        if (message.capabilities.large_context) badges.push('<span class="capability-badge large-context">📚</span>');

        if (badges.length > 0) {
            capabilitiesBadges = `<div class="capabilities-badges">${badges.join('')}</div>`;
        }
    }

    // Thinking toggle button
    let thinkingToggle = '';
    if (message.has_thinking && message.original_content) {
        const messageId = `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        thinkingToggle = `
            <button class="thinking-toggle" onclick="toggleThinkingContent('${messageId}')" title="Zeige/Verstecke Thinking-Prozess">
                <i class="fas fa-brain"></i>
            </button>
        `;
        messageDiv.setAttribute('data-message-id', messageId);
    }

    messageDiv.innerHTML = `
        <div class="message-icon">${icon}</div>
        <div class="message-content">
            <div class="message-header">
                <span class="message-author">${escapeHtml(agentName)}</span>
                <span class="message-time">${timestamp}</span>
                ${performanceInfo}
                ${thinkingToggle}
            </div>
            ${capabilitiesBadges}
            <div class="message-text filtered-content">${escapeHtml(message.content || '')}</div>
            ${message.has_thinking && message.original_content ? `
                <div class="thinking-content" style="display: none;">
                    <div class="thinking-header">
                        <i class="fas fa-brain"></i> Thinking-Prozess (ungefiltert):
                    </div>
                    <div class="thinking-text">${escapeHtml(message.original_content)}</div>
                </div>
            ` : ''}
        </div>
    `;
    
    return messageDiv;
}

// Toggle thinking content visibility
function toggleThinkingContent(messageId) {
    const messageDiv = document.querySelector(`[data-message-id="${messageId}"]`);
    if (!messageDiv) return;

    const filteredContent = messageDiv.querySelector('.filtered-content');
    const thinkingContent = messageDiv.querySelector('.thinking-content');
    const toggleButton = messageDiv.querySelector('.thinking-toggle');

    if (!filteredContent || !thinkingContent || !toggleButton) return;

    const isShowingThinking = thinkingContent.style.display !== 'none';

    if (isShowingThinking) {
        // Zeige gefilterte Version
        thinkingContent.style.display = 'none';
        filteredContent.style.display = 'block';
        toggleButton.innerHTML = '<i class="fas fa-brain"></i>';
        toggleButton.classList.remove('active');
        toggleButton.title = 'Zeige Thinking-Prozess';
    } else {
        // Zeige Thinking-Version
        thinkingContent.style.display = 'block';
        filteredContent.style.display = 'none';
        toggleButton.innerHTML = '<i class="fas fa-eye-slash"></i>';
        toggleButton.classList.add('active');
        toggleButton.title = 'Zeige gefilterte Version';
    }
}

// Scroll to bottom of chat
function scrollToBottom() {
    const chatMessages = document.getElementById('chat-messages');
    if (chatMessages) {
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
}

// Show/hide loading overlay
function showLoading(show) {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
        overlay.style.display = show ? 'flex' : 'none';
    }
}

// Utility function to escape HTML
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Utility function to format file size
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Auto-refresh status every 30 seconds
setInterval(() => {
    if (!chatStatus.isRunning) {
        checkOllamaStatus();
    }
}, 30000);

// Socket.IO-Status aktualisieren
function updateConnectionStatus(status, className = '') {
    const connectionStatus = document.getElementById('connection-status');
    if (connectionStatus) {
        connectionStatus.textContent = status;
        if (className) {
            connectionStatus.className = `status-indicator ${className}`;
        }
    }
}

// Fallback-Polling aktivieren, wenn Socket.IO nicht funktioniert
function enableFallbackPolling() {
    if (pollingInterval) return; // Bereits aktiv
    
    useFallbackPolling = true;
    console.log('Enabling fallback polling mechanism');
    addLog('Using fallback communication method', 'info');
    
    // Statusabfrage alle 5 Sekunden
    pollingInterval = setInterval(async () => {
        try {
            // Status abfragen
            const statusResponse = await fetch('/api/status');
            if (statusResponse.ok) {
                const status = await statusResponse.json();
                chatStatus = status;
                updateChatStatus();
                updateAgentCards();
            }
            
            // Konversation abfragen, wenn Chat läuft
            if (chatStatus.isRunning) {
                const conversationResponse = await fetch('/api/conversation');
                if (conversationResponse.ok) {
                    const history = await conversationResponse.json();
                    
                    // Nur aktualisieren, wenn sich etwas geändert hat
                    if (JSON.stringify(history) !== JSON.stringify(conversationHistory)) {
                        conversationHistory = history;
                        displayConversation();
                        if (autoScroll) {
                            scrollToBottom();
                        }
                    }
                }
            }
        } catch (error) {
            console.error('Fallback polling error:', error);
        }
    }, 5000);
}

// Socket.IO-Verbindung nach einigen fehlgeschlagenen Versuchen auf Fallback umstellen
function setupFallbackDetection() {
    let reconnectAttempts = 0;
    
    if (!socket) return;
    
    socket.on('reconnect_attempt', (attemptNumber) => {
        reconnectAttempts = attemptNumber;
        
        // Nach 3 fehlgeschlagenen Versuchen Fallback aktivieren
        if (reconnectAttempts >= 3 && !useFallbackPolling) {
            enableFallbackPolling();
        }
    });
    
    socket.on('connect', () => {
        // Bei erfolgreicher Verbindung Fallback deaktivieren
        if (useFallbackPolling && pollingInterval) {
            clearInterval(pollingInterval);
            pollingInterval = null;
            useFallbackPolling = false;
            console.log('Socket.IO connected, disabling fallback polling');
            addLog('Real-time connection restored', 'success');
        }
    });
}

// Neue Funktion zum Laden der Agenten-Informationen
async function loadAgentInfo() {
    try {
        const response = await fetch('/api/status');
        if (!response.ok) {
            throw new Error(`HTTP error ${response.status}`);
        }
        
        const data = await response.json();
        console.log('Loaded status data:', data);
        
        if (data && data.agents) {
            // Aktualisiere globalen Status
            chatStatus = data;
            
            // Aktualisiere UI
            updateAgentCards(data.agents);
            updateChatStatus();
        }
    } catch (error) {
        console.error('Error loading agent info:', error);
        addLog(`Failed to load agent information: ${error.message}`, 'error');
    }
}

// Neue Funktion zum Abrufen und Anzeigen laufender Modelle
async function fetchRunningModels() {
    try {
        console.log('Fetching running models...');
        const response = await fetch('/api/ollama/ps');
        if (!response.ok) {
            throw new Error(`HTTP error ${response.status}`);
        }
        
        const data = await response.json();
        console.log('Running models API response:', data);
        console.log('API response structure:', JSON.stringify(data));
        
        if (data.models && Array.isArray(data.models)) {
            console.log(`Found ${data.models.length} running models`);
            runningModels = data.models;
        } else {
            console.warn('No models array in API response or it is empty:', data);
            runningModels = [];
        }
        
        displayRunningModels();
    } catch (error) {
        console.error('Error fetching running models:', error);
        addLog(`Failed to fetch running models: ${error.message}`, 'error');
    }
}

// Funktion zum Anzeigen der laufenden Modelle
function displayRunningModels() {
    const container = document.getElementById('running-models-container');
    if (!container) return;
    
    if (!runningModels || runningModels.length === 0) {
        container.innerHTML = '<div class="no-models">Keine laufenden Modelle gefunden</div>';
        return;
    }
    
    let html = '<div class="running-models-list">';
    
    runningModels.forEach(model => {
        const name = model.name || 'Unbekannt';
        const digest = model.digest ? model.digest.substring(0, 8) : 'N/A';
        const size = formatFileSize(model.size || 0);
        const size_vram = formatFileSize(model.size_vram || 0);
        const paramSize = model.details?.parameter_size || 'N/A';
        const quantLevel = model.details?.quantization_level || 'N/A';
        
        html += `
            <div class="running-model-item">
                <div class="model-name">${name}</div>
                <div class="model-details">
                    <span class="model-id">ID: ${digest}</span>
                    <span class="model-size">Größe: ${size}</span>
                    <span class="model-vram">VRAM: ${size_vram}</span>
                    <span class="model-param">Parameter: ${paramSize}</span>
                    <span class="model-quant">Quantisierung: ${quantLevel}</span>
                </div>
            </div>
        `;
    });
    
    html += '</div>';
    container.innerHTML = html;
}

// Auto-refresh running models every 30 seconds
setInterval(() => {
    fetchRunningModels();
}, 30000);