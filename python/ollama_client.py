import requests
import time
import json
import logging
import os
from typing import Dict, List, Optional
import re
from dotenv import load_dotenv

class OllamaClient:
    """Client for interacting with Ollama API"""
    
    def __init__(self, base_url: str = "http://*********:11223"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        # Use environment variable for session timeout with higher default for large models
        self.session.timeout = int(os.getenv('OLLAMA_SESSION_TIMEOUT', 120))
        
        # Speichere die letzten Token-Informationen
        self.last_token_info = {}
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
        debug_mode = os.getenv('APP_DEBUG_MODE', 'false').lower() == 'true'
        if debug_mode:
            self.logger.setLevel(logging.DEBUG)
            self.logger.info("Debug mode is ON for OllamaClient (Full Details).") # Keep this INFO to ensure it's visible when debug is on
        else:
            self.logger.setLevel(logging.INFO)
    
    def test_connection(self) -> bool:
        """Test if Ollama server is accessible"""
        try:
            response = self.session.get(f"{self.base_url}/api/tags")
            response.raise_for_status()
            self.logger.info("Successfully connected to Ollama server")
            return True
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Failed to connect to Ollama server: {e}")
            return False
    
    def get_models(self) -> List[Dict]:
        """Get list of available models"""
        try:
            response = self.session.get(f"{self.base_url}/api/tags")
            response.raise_for_status()
            data = response.json()
            models = data.get('models', [])
            self.logger.info(f"Found {len(models)} available models")
            return models
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Failed to get models: {e}")
            return []
    
    def filter_thinking_content(self, text: str) -> str:
        """Filter out thinking/reasoning content from model responses while preserving the actual conversation"""
        if not text:
            return text
        
        # Enhanced thinking patterns to remove
        thinking_patterns = [
            r'<think>.*?</think>',  # <think> tags
            r'<thinking>.*?</thinking>',  # <thinking> tags
            r'\*thinking\*.*?\*/thinking\*',  # *thinking* tags
            r'\[thinking\].*?\[/thinking\]',  # [thinking] tags
            r'\(thinking:.*?\)',  # (thinking: ...) patterns
            r'\*.*?denkt.*?\*',  # *denkt* patterns
            r'\*.*?überlegt.*?\*',  # *überlegt* patterns
            r'\*.*?thinks.*?\*',  # *thinks* patterns
            r'\*.*?reasoning.*?\*',  # *reasoning* patterns
            r'^\s*Ich denke.*?(?=\n\n|\n[A-Z]|$)',  # "Ich denke" followed by paragraph break or capital letter
            r'^\s*Let me think.*?(?=\n\n|\n[A-Z]|$)',  # "Let me think" patterns
            r'^\s*Hmm.*?(?=\n\n|\n[A-Z]|$)',  # "Hmm" patterns
            r'^\s*Well.*?(?=\n\n|\n[A-Z]|$)',  # "Well" patterns
            r'^\s*Also.*?(?=\n\n|\n[A-Z]|$)',  # "Also" patterns
            r'^\s*Lass mich.*?(?=\n\n|\n[A-Z]|$)',  # "Lass mich" patterns
            r'^\s*Zunächst.*?(?=\n\n|\n[A-Z]|$)',  # "Zunächst" patterns
            r'^\s*Erstmal.*?(?=\n\n|\n[A-Z]|$)',  # "Erstmal" patterns
            r'\(.*?überlegt.*?\)',  # (überlegt) patterns
            r'\(.*?denkt.*?\)',  # (denkt) patterns
            r'\[.*?thinking.*?\]',  # [thinking] variations
            r'\[.*?überlegt.*?\]',  # [überlegt] patterns
        ]
        
        filtered_text = text
        for pattern in thinking_patterns:
            filtered_text = re.sub(pattern, '', filtered_text, flags=re.DOTALL | re.IGNORECASE | re.MULTILINE)
        
        # Clean up extra whitespace and empty lines
        filtered_text = re.sub(r'\n\s*\n\s*\n+', '\n\n', filtered_text)
        filtered_text = re.sub(r'^\s*\n+', '', filtered_text)
        filtered_text = filtered_text.strip()
        
        return filtered_text
    
    def _get_model_options(self, model: str) -> Dict:
        """Get optimized options for the specified model"""
        # Determine if this is a large model based on name
        is_large_model = any(x in model.lower() for x in ['32b', '27b', '24b', '70b', 'deepseek-r1', 'qwen2.5'])
        
        # Determine mode based on model size and environment variable
        mode = os.getenv('OLLAMA_MODE', 'hybrid' if is_large_model else 'default')
        
        # Adjust GPU layers based on model size and available VRAM
        # For large models in hybrid mode, we want to maximize GPU usage
        if mode == 'hybrid' and is_large_model:
            num_gpu = int(os.getenv('OLLAMA_NUM_GPU', 100))  # Use maximum layers on GPU
            num_thread = int(os.getenv('OLLAMA_NUM_THREAD', 8))
        else:
            num_gpu = int(os.getenv('OLLAMA_NUM_GPU', 1))
            num_thread = int(os.getenv('OLLAMA_NUM_THREAD', 8))
        
        # Adjust context size based on model size
        num_ctx = int(os.getenv('OLLAMA_NUM_CTX', 8192 if is_large_model else 4096))
        
        # Adjust batch size based on model size
        num_batch = int(os.getenv('OLLAMA_NUM_BATCH', 1024 if is_large_model else 512))
        
        # Configure options
        options = {
            "num_gpu": num_gpu,
            "num_thread": num_thread,
            "mirostat": int(os.getenv('OLLAMA_MIROSTAT', 0)),
            "seed": int(os.getenv('OLLAMA_SEED', 0)),
            "temperature": float(os.getenv('OLLAMA_TEMPERATURE', 0.7)),
            "top_k": int(os.getenv('OLLAMA_TOP_K', 40)),
            "top_p": float(os.getenv('OLLAMA_TOP_P', 0.9)),
            "tfs_z": float(os.getenv('OLLAMA_TFS_Z', 1.0)),
            "typical_p": float(os.getenv('OLLAMA_TYPICAL_P', 1.0)),
            "repeat_penalty": float(os.getenv('OLLAMA_REPEAT_PENALTY', 1.1)),
            "num_ctx": num_ctx,
            "num_batch": num_batch,
            "num_keep": int(os.getenv('OLLAMA_NUM_KEEP', 0)),
            "num_predict": int(os.getenv('OLLAMA_NUM_PREDICT', 1024 if is_large_model else 512)),
            "mode": mode
        }
        
        num_predict = options['num_predict'] # get num_predict from options
        self.logger.debug(f"Model {model} configured with mode={mode}, num_gpu={num_gpu}, num_ctx={num_ctx}, num_batch={num_batch}, num_predict={num_predict}")
        return options
    
    def generate_response(self, model: str, prompt: str, system_prompt: str = None, max_retries: int = 3) -> Optional[str]:
        """Generate a response using the specified model with retry logic for multiple models"""
        # Determine if this is a large model based on name
        is_large_model = any(x in model.lower() for x in ['32b', '27b', '24b', '70b', 'deepseek-r1', 'qwen2.5'])
        
        # Get timeout from environment variable with higher default for large models
        generate_timeout = int(os.getenv('OLLAMA_GENERATE_TIMEOUT', 3000 if is_large_model else 120))
        
        self.logger.debug(f"Using timeout of {generate_timeout}s for model {model}")
        
        # Zurücksetzen der Token-Informationen für diesen Aufruf
        self.last_token_info = {}
        
        for attempt in range(max_retries):
            try:
                # Get optimized options for this model
                options = self._get_model_options(model)
                
                payload = {
                    "model": model,
                    "prompt": prompt,
                    "stream": False,
                    "options": options
                }
                
                if system_prompt:
                    payload["system"] = system_prompt
                
                self.logger.debug(f"Sending request to model {model} (attempt {attempt + 1}/{max_retries})...")
                
                response = self.session.post(
                    f"{self.base_url}/api/generate",
                    json=payload,
                    headers={"Content-Type": "application/json"},
                    timeout=generate_timeout
                )
                response.raise_for_status()
                
                data = response.json()
                generated_text = data.get('response', '').strip()
                
                # Token-Informationen speichern
                self.last_token_info = {
                    'prompt_tokens': data.get('prompt_eval_count', 0),
                    'completion_tokens': data.get('eval_count', 0),
                    'total_tokens': data.get('prompt_eval_count', 0) + data.get('eval_count', 0),
                    'eval_duration': data.get('eval_duration', 0) / 1000000000 if data.get('eval_duration') else 0  # Umrechnung von ns in s
                }
                
                # Filter thinking content
                filtered_text = self.filter_thinking_content(generated_text)
                
                self.logger.info(f"Generated response with model {model}: {len(filtered_text)} characters (attempt {attempt + 1})")
                self.logger.info(f"Token stats: {self.last_token_info['prompt_tokens']} prompt, {self.last_token_info['completion_tokens']} completion, {self.last_token_info['total_tokens']} total")
                self.logger.info(f"Model eval time: {self.last_token_info['eval_duration']:.2f}s")
                return filtered_text
                
            except requests.exceptions.Timeout:
                self.logger.warning(f"Timeout for model {model} on attempt {attempt + 1}/{max_retries}. Timeout was set to {generate_timeout}s")
                if attempt < max_retries - 1:
                    backoff_time = 2 ** attempt
                    self.logger.debug(f"Waiting {backoff_time}s before retrying...")
                    time.sleep(backoff_time)  # Exponential backoff
                    continue
            except requests.exceptions.RequestException as e:
                self.logger.error(f"Failed to generate response with model {model} on attempt {attempt + 1}/{max_retries}: {e}")
                if attempt < max_retries - 1:
                    backoff_time = 2 ** attempt
                    self.logger.debug(f"Waiting {backoff_time}s before retrying...")
                    time.sleep(backoff_time)  # Exponential backoff
                    continue
        
        self.logger.error(f"All {max_retries} attempts failed for model {model}")
        return None
    
    def chat_completion(self, model: str, messages: List[Dict], system_prompt: str = None) -> Optional[str]:
        """Generate a chat completion using the specified model"""
        # Determine if this is a large model based on name
        is_large_model = any(x in model.lower() for x in ['32b', '27b', '24b', '70b', 'deepseek-r1', 'qwen2.5'])
        
        # Get timeout from environment variable with higher default for large models
        chat_timeout = int(os.getenv('OLLAMA_CHAT_TIMEOUT', 3000 if is_large_model else 120))
        
        try:
            # Get optimized options for this model
            options = self._get_model_options(model)
            
            payload = {
                "model": model,
                "messages": messages,
                "stream": False,
                "options": options
            }
            
            if system_prompt:
                payload["system"] = system_prompt
            
            self.logger.debug(f"Sending chat request to model {model} with timeout {chat_timeout}s...")
            
            response = self.session.post(
                f"{self.base_url}/api/chat",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=chat_timeout
            )
            response.raise_for_status()
            
            data = response.json()
            message = data.get('message', {})
            content = message.get('content', '').strip()
            
            self.logger.debug(f"Chat completion with model {model}: {len(content)} characters")
            return content
            
        except requests.exceptions.Timeout:
            self.logger.error(f"Timeout for chat completion with model {model}. Timeout was set to {chat_timeout}s")
            return None
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Failed to get chat completion with model {model}: {e}")
            return None
    
    def pull_model(self, model_name: str) -> bool:
        """Pull a model if not available"""
        try:
            payload = {"name": model_name}
            response = self.session.post(
                f"{self.base_url}/api/pull",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            response.raise_for_status()
            self.logger.info(f"Successfully pulled model {model_name}")
            return True
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Failed to pull model {model_name}: {e}")
            return False
    
    def get_model_info(self, model_name: str) -> Optional[Dict]:
        """Get information about a specific model"""
        try:
            payload = {"name": model_name}
            response = self.session.post(
                f"{self.base_url}/api/show",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Failed to get model info for {model_name}: {e}")
            return None
    
    def get_running_models(self) -> List[Dict]:
        """Get information about currently running models (similar to 'ollama ps')"""
        try:
            response = self.session.get(f"{self.base_url}/api/ps")
            response.raise_for_status()
            data = response.json()
            models = data.get('models', [])
            
            # Kompakte Log-Ausgabe für laufende Modelle
            if models:
                model_names = [model.get('name', 'unknown') for model in models]
                self.logger.debug(f"Running models: {', '.join(model_names)}")
            else:
                self.logger.debug("No running models found")
            
            return models
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Failed to get running models: {e}")
            return []
    
    def get_last_token_info(self) -> Dict:
        """Gibt die Token-Informationen des letzten generate_response-Aufrufs zurück"""
        return self.last_token_info