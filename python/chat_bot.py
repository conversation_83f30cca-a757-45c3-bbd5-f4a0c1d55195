import asyncio
import json
import logging
import time
import os
import sys
import argparse
from datetime import datetime
from typing import Dict, List, Optional
from ollama_client import OllamaClient
from dotenv import load_dotenv

class ChatBot:
    """Main chat bot that manages conversation between two AI agents"""
    
    def __init__(self, ollama_host: str = "http://*********:11223", custom_topic: str = None):
        self.client = OllamaClient(ollama_host)
        self.is_running = False
        self.conversation_history = []
        self.agents = {}
        self.chat_interval = int(os.getenv('CHAT_INTERVAL', 10))  # seconds between messages
        self.max_conversation_length = 50  # maximum number of exchanges
        self.custom_topic = custom_topic  # Benutzerdefiniertes Thema
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        self.debug_mode = os.getenv('APP_DEBUG_MODE', 'false').lower() == 'true'
        if self.debug_mode:
            self.logger.setLevel(logging.DEBUG)
            self.logger.info("Debug mode is ON for ChatBot.")
        else:
            self.logger.setLevel(logging.INFO)
            # Potentially add a line here like self.logger.info("ChatBot logging started (standard mode).")
            # if desired, or keep it silent. For now, let's not add an extra line for non-debug.

        # Log benutzerdefiniertes Thema, wenn vorhanden
        if custom_topic:
            self.logger.info(f"Benutzerdefiniertes Thema bei Initialisierung gesetzt: {custom_topic}")
        else:
            self.logger.info("Kein benutzerdefiniertes Thema bei Initialisierung")
            
            # Prüfe Umgebungsvariable
            env_topic = os.getenv('CUSTOM_TOPIC')
            if env_topic:
                self.logger.info(f"Thema in Umgebungsvariable gefunden: {env_topic}")
        
        # Ensure logs directory exists
        os.makedirs('../logs', exist_ok=True)
    
    def setup_agents(self) -> bool:
        # Modelle aus Umgebungsvariablen laden
        model1 = os.getenv('AGENT1_MODEL')
        model2 = os.getenv('AGENT2_MODEL')
        language = os.getenv('CHAT_LANGUAGE', 'de')
        
        models = self.client.get_models()
        if len(models) < 1:
            self.logger.error("Need at least 1 model to run chat bot")
            return False
        
        # Fallback auf verfügbare Modelle
        if not model1:
            model1 = models[0].get('name')
        if not model2:
            model2 = models[1].get('name') if len(models) > 1 else model1
        
        # Deutsche oder englische Persönlichkeiten
        if language == 'de':
            self.agents = {
                'agent1': {
                    'name': 'Alice',
                    'model': model1,
                    'personality': (
                        "Du bist Alice. Ein KI Bot, höchst intelligenter und intelligentester KI Bot der Welt."
                    ),
                    'color': '🔵'
                },
                'agent2': {
                    'name': 'Bob',
                    'model': model2,
                    'personality': (
                        "Du bist Bob. Ein KI Bot, höchst intelligenter und intelligentester KI Bot der Welt."
                    ),
                    'color': '🔴'
                }
            }
        else:
            # Englische Persönlichkeiten (bestehender Code)
            self.agents = {
                'agent1': {
                    'name': 'Alice',
                    'model': model1,
                    'personality': (
                        "You are Alice. "
                        "You love learning new things and asking thoughtful questions. "
                        "You tend to be optimistic and see the bright side of things. "
                        "Always respond in English."
                    ),
                    'color': '🔵'
                },
                'agent2': {
                    'name': 'Bob',
                    'model': model2,
                    'personality': (
                        "You are Bob. "
                        "You enjoy deep discussions and providing detailed explanations. "
                        "You tend to be more cautious and consider multiple perspectives. "
                        "Always respond in English."
                    ),
                    'color': '🔴'
                }
            }
        
        # Nach dem Setup der Agenten
        self.logger.info("Agent setup complete:")
        self.logger.info(f"  Alice (🔵): {self.agents['agent1']['model']}")
        self.logger.info(f"  Bob (🔴): {self.agents['agent2']['model']}")
        self.logger.info(f"  Language: {language}")
        
        # Send agent info to web interface via stdout
        # Wichtig: Die Ausgabe auf einer eigenen Zeile für besseres Parsing
        agent_info = {
            'agent1': {
                'name': self.agents['agent1']['name'],
                'model': self.agents['agent1']['model'],
                'status': 'ready'
            },
            'agent2': {
                'name': self.agents['agent2']['name'], 
                'model': self.agents['agent2']['model'],
                'status': 'ready'
            }
        }
        
        # Flush stdout um sicherzustellen, dass vorherige Ausgaben abgeschlossen sind
        sys.stdout.flush()
        
        # Ausgabe auf einer eigenen Zeile
        print(f"\nAGENT_INFO:{json.dumps(agent_info)}\n")
        sys.stdout.flush()
        
        return True
    
    def start_conversation_topic(self) -> str:
        """Generate an interesting conversation starter"""
        # Wenn ein benutzerdefiniertes Thema festgelegt wurde, verwende es
        if self.custom_topic:
            self.logger.info(f"Verwende benutzerdefiniertes Thema aus Argument: {self.custom_topic}")
            return self.custom_topic
            
        # Ansonsten Umgebungsvariable prüfen
        env_topic = os.getenv('CUSTOM_TOPIC')
        if env_topic:
            self.logger.info(f"Verwende Thema aus Umgebungsvariable: {env_topic}")
            return env_topic
            
        # Wenn kein benutzerdefiniertes Thema vorhanden ist, zufälliges Thema auswählen
        language = os.getenv('CHAT_LANGUAGE', 'de')
        self.logger.info("Kein benutzerdefiniertes Thema gefunden, wähle zufälliges Thema")
        
        if language == 'de':
            topics = [
                "Was denkst du über die Zukunft der künstlichen Intelligenz?",
                "Wenn du überall auf der Welt hinreisen könntest, wohin würdest du gehen und warum?",
                "Was ist das Interessanteste, was du kürzlich gelernt hast?",
                "Glaubst du, dass Menschen jemals auf anderen Planeten leben werden?",
                "Welche Rolle sollte Technologie in der Bildung spielen?",
                "Wie können wir deiner Meinung nach den Klimawandel lösen?",
                "Was macht einen guten Freund aus?",
                "Wenn du eine Superkraft haben könntest, welche wäre das?",
                "Was ist deine Meinung zur Bedeutung von Kreativität?",
                "Wie wird sich die Kommunikation deiner Meinung nach in Zukunft entwickeln?"
            ]
        else:
            topics = [
                "What do you think about the future of artificial intelligence?",
                "If you could travel anywhere in the world, where would you go and why?",
                "What's the most interesting thing you've learned recently?",
                "Do you think humans will ever live on other planets?",
                "What role should technology play in education?",
                "How do you think we can solve climate change?",
                "What makes a good friend?",
                "If you could have any superpower, what would it be?",
                "What's your opinion on the importance of creativity?",
                "How do you think communication will evolve in the future?"
            ]
        
        import random
        random_topic = random.choice(topics)
        self.logger.info(f"Zufällig ausgewähltes Thema: {random_topic}")
        return random_topic
    
    def get_conversation_context(self, max_messages: int = 6) -> str:
        """Get recent conversation context for the agents"""
        if not self.conversation_history:
            return "This is the beginning of our conversation."
        
        recent_messages = self.conversation_history[-max_messages:]
        context = "Recent conversation:\n"
        
        for msg in recent_messages:
            speaker = msg['agent']
            content = msg['content']
            context += f"{speaker}: {content}\n"
        
        return context
    
    def generate_response(self, agent_key: str, context: str) -> Optional[str]:
        """Generate a response for the specified agent"""
        agent = self.agents[agent_key]
        
        # Create the prompt with personality and context
        # Let the model think freely, but filter the output
        language = os.getenv('CHAT_LANGUAGE', 'de')
        suffix = f"Antworte als {agent['name']}:" if language == 'de' else f"Respond as {agent['name']}:"
        prompt = f"{agent['personality']}\n\n{context}\n\n{suffix}"
        
        # Zeitmessung starten
        start_time = time.time()
        
        response = self.client.generate_response(
            model=agent['model'],
            prompt=prompt
        )
        
        # Zeitmessung beenden
        end_time = time.time()
        generation_time = end_time - start_time
        
        # Token-Informationen abrufen (falls verfügbar)
        token_info = self.client.get_last_token_info()
        
        return {
            'text': response,
            'generation_time': generation_time,
            'token_info': token_info
        }
    
    def log_conversation(self, agent_name: str, message_data):
        """Log conversation to file"""
        timestamp = datetime.now().isoformat()
        
        # Überprüfen, ob message_data ein Wörterbuch oder ein String ist
        if isinstance(message_data, dict):
            message = message_data.get('text', '')
            generation_time = message_data.get('generation_time', 0)
            token_info = message_data.get('token_info', {})
        else:
            message = message_data
            generation_time = 0
            token_info = {}
        
        log_entry = {
            'timestamp': timestamp,
            'agent': agent_name,
            'content': message,
            'generation_time': round(generation_time, 2) if generation_time else None,
            'token_info': token_info
        }
        
        # Add to memory
        self.conversation_history.append(log_entry)
        
        # Write to file
        log_filename = f"../logs/chat_{datetime.now().strftime('%Y%m%d')}.json"
        try:
            # Read existing log
            if os.path.exists(log_filename):
                with open(log_filename, 'r') as f:
                    logs = json.load(f)
            else:
                logs = []
            
            # Append new entry
            logs.append(log_entry)
            
            # Write back
            with open(log_filename, 'w') as f:
                json.dump(logs, f, indent=2)
                
        except Exception as e:
            self.logger.error(f"Failed to write log: {e}")
    
    async def run_conversation(self, max_exchanges: int = None):
        """Run the conversation between the two agents"""
        if not self.setup_agents():
            return
        
        if max_exchanges is None:
            max_exchanges = self.max_conversation_length
        
        self.is_running = True
        self.conversation_history = []
        
        # Start with a topic
        starter_topic = self.start_conversation_topic()
        self.logger.info(f"🎬 Starting conversation with topic: {starter_topic}")
        
        # Log the starter topic as the first message
        self.log_conversation("System", f"Gesprächsthema: {starter_topic}")
        
        # Alice starts the conversation
        current_agent = 'agent1'
        context = f"Let's discuss this topic: {starter_topic}"
        
        try:
            for exchange in range(max_exchanges):
                if not self.is_running:
                    break
                
                agent = self.agents[current_agent]
                agent_name = agent['name']
                color = agent['color']
                
                self.logger.info(f"{color} {agent_name} is thinking...")
                
                # Generate response
                response_data = self.generate_response(current_agent, context)
                
                if response_data and (isinstance(response_data, dict) and response_data.get('text')) or (isinstance(response_data, str) and response_data):
                    # Extrahiere den Text und die Metadaten
                    if isinstance(response_data, dict):
                        response_text = response_data.get('text', '')
                        generation_time = response_data.get('generation_time', 0)
                        token_info = response_data.get('token_info', {})
                        
                        # Log Performance-Informationen
                        token_count = token_info.get('total_tokens', 'unbekannt')
                        self.logger.info(f"{color} {agent_name} antwortete in {generation_time:.2f}s mit {token_count} Tokens")
                    else:
                        response_text = response_data
                    
                    # Log and display
                    self.log_conversation(agent_name, response_data)
                    print(f"\n{color} {agent_name}: {response_text}")
                    
                    # Update context for next agent
                    context = self.get_conversation_context()
                    
                    # Switch to other agent
                    current_agent = 'agent2' if current_agent == 'agent1' else 'agent1'
                    
                    # Wait before next message
                    if self.chat_interval > 0:
                        self.logger.debug(f"Warte {self.chat_interval}s vor der nächsten Nachricht...")
                        await asyncio.sleep(self.chat_interval)
                else:
                    self.logger.error(f"Failed to generate response for {agent_name}")
                    break
            
            self.logger.info("🏁 Conversation completed!")
            
        except KeyboardInterrupt:
            self.logger.info("\n⏹️ Conversation stopped by user")
        except Exception as e:
            self.logger.error(f"Error during conversation: {e}")
        finally:
            self.is_running = False
    
    def stop_conversation(self):
        """Stop the current conversation"""
        self.is_running = False
        self.logger.info("🛑 Stopping conversation...")
    
    def get_status(self) -> Dict:
        """Get current chat bot status"""
        return {
            'is_running': self.is_running,
            'conversation_length': len(self.conversation_history),
            'agents': {
                key: {
                    'name': agent['name'],
                    'model': agent['model'],
                    'color': agent['color']
                }
                for key, agent in self.agents.items()
            },
            'last_message': self.conversation_history[-1] if self.conversation_history else None
        }
    
    def get_conversation_history(self) -> List[Dict]:
        """Get the full conversation history"""
        return self.conversation_history.copy()

    def get_running_models(self) -> List[Dict]:
        """Get information about currently running models"""
        return self.client.get_running_models()

async def main():
    """Main function to run the chat bot"""
    load_dotenv()
    # Befehlszeilenargumente verarbeiten
    parser = argparse.ArgumentParser(description='KI Chat Bot mit zwei LLMs')
    parser.add_argument('--topic', type=str, help='Benutzerdefiniertes Gesprächsthema')
    parser.add_argument('--interval', type=int, help='Wartezeit zwischen Nachrichten in Sekunden')
    args = parser.parse_args()
    
    print("🤖 AI Chat Bot - Two LLMs Conversation")
    print("======================================")
    
    # Debug: Zeige alle relevanten Umgebungsvariablen
    env_topic = os.getenv('CUSTOM_TOPIC')
    env_interval = os.getenv('CHAT_INTERVAL')
    print(f"🔍 Debug - Umgebungsvariablen:")
    print(f"   - CUSTOM_TOPIC: {env_topic}")
    print(f"   - AGENT1_MODEL: {os.getenv('AGENT1_MODEL')}")
    print(f"   - AGENT2_MODEL: {os.getenv('AGENT2_MODEL')}")
    print(f"   - CHAT_LANGUAGE: {os.getenv('CHAT_LANGUAGE', 'de')}")
    print(f"   - CHAT_INTERVAL: {env_interval}")
    
    # Initialize chat bot with optional custom topic
    custom_topic = args.topic
    if custom_topic:
        print(f"📝 Benutzerdefiniertes Thema von Befehlszeile: {custom_topic}")
    elif env_topic:
        print(f"📝 Benutzerdefiniertes Thema aus Umgebungsvariable: {env_topic}")
        # Setze explizit das Thema aus der Umgebungsvariable
        custom_topic = env_topic
    else:
        print("ℹ️ Kein benutzerdefiniertes Thema gefunden, es wird ein zufälliges Thema verwendet")
    
    chat_bot = ChatBot(custom_topic=custom_topic)
    
    # Setze das Intervall, wenn es über die Befehlszeile angegeben wurde
    if args.interval is not None:
        chat_bot.chat_interval = args.interval
        print(f"⏱️ Wartezeit zwischen Nachrichten auf {args.interval} Sekunden gesetzt")
    
    # Test connection first
    if not chat_bot.client.test_connection():
        print("❌ Failed to connect to Ollama server")
        return
    
    print("✅ Connected to Ollama server")
    
    # Run conversation
    try:
        await chat_bot.run_conversation()
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")

if __name__ == "__main__":
    asyncio.run(main())