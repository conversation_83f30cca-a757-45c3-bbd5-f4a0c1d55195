import pytest
import sys
import os
from ollama_client import OllamaClient

def test_ollama_connection():
    """Test basic connection to Ollama server"""
    client = OllamaClient()
    assert client.test_connection(), "Failed to connect to Ollama server"

def test_get_models():
    """Test retrieving available models"""
    client = OllamaClient()
    models = client.get_models()
    assert isinstance(models, list), "Models should be returned as a list"
    print(f"Available models: {[model.get('name', 'Unknown') for model in models]}")

def test_model_generation():
    """Test text generation with available models"""
    client = OllamaClient()
    models = client.get_models()
    
    if not models:
        pytest.skip("No models available for testing")
    
    # Use the first available model
    model_name = models[0].get('name')
    if not model_name:
        pytest.skip("No valid model name found")
    
    prompt = "Hello, how are you today?"
    response = client.generate_response(model_name, prompt)
    
    assert response is not None, f"Failed to generate response with model {model_name}"
    assert len(response) > 0, "Response should not be empty"
    print(f"Model {model_name} response: {response[:100]}...")

def test_chat_completion():
    """Test chat completion functionality"""
    client = OllamaClient()
    models = client.get_models()
    
    if not models:
        pytest.skip("No models available for testing")
    
    model_name = models[0].get('name')
    if not model_name:
        pytest.skip("No valid model name found")
    
    messages = [
        {"role": "user", "content": "What is the capital of France?"}
    ]
    
    response = client.chat_completion(model_name, messages)
    
    assert response is not None, f"Failed to get chat completion with model {model_name}"
    assert len(response) > 0, "Chat response should not be empty"
    print(f"Chat completion response: {response[:100]}...")

def test_model_info():
    """Test getting model information"""
    client = OllamaClient()
    models = client.get_models()
    
    if not models:
        pytest.skip("No models available for testing")
    
    model_name = models[0].get('name')
    if not model_name:
        pytest.skip("No valid model name found")
    
    info = client.get_model_info(model_name)
    assert info is not None, f"Failed to get info for model {model_name}"
    print(f"Model {model_name} info keys: {list(info.keys()) if info else 'None'}")

def test_hybrid_mode():
    """Test the hybrid mode for large models like deepseek r1 32b"""
    # Set environment variable for hybrid mode
    os.environ['OLLAMA_MODE'] = 'hybrid'
    os.environ['OLLAMA_NUM_CTX'] = '8192'
    os.environ['OLLAMA_NUM_BATCH'] = '1024'
    
    client = OllamaClient()
    models = client.get_models()
    
    # Find a large model to test with
    large_model = None
    for model in models:
        name = model.get('name', '').lower()
        if 'deepseek' in name or '32b' in name or '70b' in name or 'llama3' in name:
            large_model = name
            break
    
    if not large_model:
        pytest.skip("No large model available for hybrid mode testing")
    
    print(f"Testing hybrid mode with model: {large_model}")
    
    # Test with a complex prompt that requires more context
    prompt = """Explain the differences between transformer-based language models and recurrent neural networks 
    in terms of parallelization capabilities, context handling, and computational efficiency."""
    
    response = client.generate_response(large_model, prompt)
    
    assert response is not None, f"Failed to generate response with model {large_model} in hybrid mode"
    assert len(response) > 100, "Response should be substantial for a complex query"
    print(f"Hybrid mode response with {large_model}: {response[:150]}...")
    
    # Reset environment variables
    if 'OLLAMA_MODE' in os.environ:
        del os.environ['OLLAMA_MODE']
    if 'OLLAMA_NUM_CTX' in os.environ:
        del os.environ['OLLAMA_NUM_CTX']
    if 'OLLAMA_NUM_BATCH' in os.environ:
        del os.environ['OLLAMA_NUM_BATCH']

def run_interactive_test():
    """Run an interactive test to verify everything works"""
    print("=== Ollama API Test ===")
    
    client = OllamaClient()
    
    # Test connection
    print("\n1. Testing connection...")
    if not client.test_connection():
        print("❌ Connection failed!")
        return False
    print("✅ Connection successful!")
    
    # Get models
    print("\n2. Getting available models...")
    models = client.get_models()
    if not models:
        print("❌ No models available!")
        return False
    
    print(f"✅ Found {len(models)} models:")
    for i, model in enumerate(models):
        name = model.get('name', 'Unknown')
        size = model.get('size', 0)
        print(f"   {i+1}. {name} ({size} bytes)")
    
    # Test generation with first model
    print("\n3. Testing text generation...")
    model_name = models[0].get('name')
    test_prompt = "Explain what artificial intelligence is in one sentence."
    
    print(f"Using model: {model_name}")
    print(f"Prompt: {test_prompt}")
    
    response = client.generate_response(model_name, test_prompt)
    if response:
        print(f"✅ Response: {response}")
    else:
        print("❌ Generation failed!")
        return False
    
    # Test chat completion
    print("\n4. Testing chat completion...")
    messages = [
        {"role": "user", "content": "What is 2+2?"}
    ]
    
    chat_response = client.chat_completion(model_name, messages)
    if chat_response:
        print(f"✅ Chat response: {chat_response}")
    else:
        print("❌ Chat completion failed!")
        return False
    
    # Test hybrid mode if available
    print("\n5. Testing hybrid mode for large models...")
    os.environ['OLLAMA_MODE'] = 'hybrid'
    
    # Find a large model if available
    large_model = None
    for model in models:
        name = model.get('name', '').lower()
        if 'deepseek' in name or '32b' in name or '70b' in name:
            large_model = name
            break
    
    if large_model:
        print(f"Found large model for hybrid testing: {large_model}")
        hybrid_prompt = "Explain quantum computing in simple terms."
        hybrid_response = client.generate_response(large_model, hybrid_prompt)
        
        if hybrid_response:
            print(f"✅ Hybrid mode response: {hybrid_response[:150]}...")
        else:
            print("❌ Hybrid mode test failed!")
    else:
        print("⚠️ No large model found for hybrid mode testing")
    
    # Reset environment variable
    if 'OLLAMA_MODE' in os.environ:
        del os.environ['OLLAMA_MODE']
    
    print("\n🎉 All tests passed! Ollama API is working correctly.")
    return True

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        run_interactive_test()
    else:
        # Run pytest
        pytest.main([__file__, "-v"])